#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de prueba para las mejoras implementadas
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import sys
from pathlib import Path

class TestMejoras:
    def __init__(self):
        self.directorio_base = Path(__file__).parent
        self.crear_ventana_test()
        
    def crear_ventana_test(self):
        """Crea la ventana de pruebas"""
        self.root = tk.Tk()
        self.root.title("Pruebas de Mejoras - Lector Progresivo")
        self.root.geometry("600x500")
        self.root.configure(bg='#F0F8FF')
        
        # Título
        tk.Label(self.root, 
                text="🧪 Pruebas de Mejoras Implementadas", 
                font=('Arial', 16, 'bold'),
                bg='#F0F8FF',
                fg='#2F4F4F').pack(pady=20)
        
        # Frame para botones
        buttons_frame = tk.Frame(self.root, bg='#F0F8FF')
        buttons_frame.pack(pady=20, padx=20, fill='both', expand=True)
        
        # Botones de prueba
        self.crear_boton(buttons_frame, "🎮 Probar Aplicación Principal", 
                        self.probar_app_principal, 
                        "Ejecuta la aplicación principal con las mejoras")
        
        self.crear_boton(buttons_frame, "👨‍👩‍👧‍👦 Panel de Padres", 
                        self.probar_panel_padres, 
                        "Abre el panel de seguimiento para padres")
        
        self.crear_boton(buttons_frame, "🌟 Sistema de Recompensas", 
                        self.probar_recompensas, 
                        "Prueba el sistema de stickers y recompensas")
        
        self.crear_boton(buttons_frame, "📊 Verificar Archivos", 
                        self.verificar_archivos, 
                        "Verifica que todos los archivos estén presentes")
        
        self.crear_boton(buttons_frame, "🔧 Instalar Dependencias", 
                        self.instalar_dependencias, 
                        "Instala las dependencias necesarias")
        
        # Área de información
        info_frame = tk.LabelFrame(self.root, text="Mejoras Implementadas", 
                                  font=('Arial', 12, 'bold'), bg='#F0F8FF')
        info_frame.pack(pady=20, padx=20, fill='x')
        
        mejoras = [
            "✅ Funcionalidad de sonido removida",
            "✅ Bug de spam de Enter corregido",
            "✅ Inteligencia adaptativa (ayuda después de 3 fallos)",
            "✅ Panel de seguimiento para padres",
            "✅ Sistema de recompensas con stickers",
            "✅ Diseño más infantil y colorido",
            "✅ Banco de preguntas expandido",
            "✅ Reinicio semanal de estrellitas",
            "✅ Celebraciones mejoradas"
        ]
        
        for mejora in mejoras:
            tk.Label(info_frame, text=mejora, 
                    font=('Arial', 10), 
                    bg='#F0F8FF', 
                    anchor='w').pack(anchor='w', padx=10, pady=2)
    
    def crear_boton(self, parent, texto, comando, descripcion):
        """Crea un botón con descripción"""
        frame = tk.Frame(parent, bg='#F0F8FF')
        frame.pack(fill='x', pady=5)
        
        btn = tk.Button(frame, 
                       text=texto,
                       font=('Arial', 12, 'bold'),
                       bg='#87CEEB',
                       fg='#2F4F4F',
                       padx=20,
                       pady=10,
                       command=comando)
        btn.pack(side='left')
        
        tk.Label(frame, 
                text=descripcion,
                font=('Arial', 10),
                bg='#F0F8FF',
                fg='#666666').pack(side='left', padx=10)
    
    def probar_app_principal(self):
        """Ejecuta la aplicación principal"""
        try:
            subprocess.Popen([sys.executable, 'lector.py'])
            messagebox.showinfo("Info", "Aplicación principal iniciada")
        except Exception as e:
            messagebox.showerror("Error", f"Error al iniciar aplicación: {e}")
    
    def probar_panel_padres(self):
        """Ejecuta el panel de padres"""
        try:
            subprocess.Popen([sys.executable, 'panel_padres_mejorado.py'])
            messagebox.showinfo("Info", "Panel de padres iniciado")
        except Exception as e:
            messagebox.showerror("Error", f"Error al iniciar panel: {e}")
    
    def probar_recompensas(self):
        """Muestra información sobre el sistema de recompensas"""
        info = """
🌟 Sistema de Recompensas Implementado:

• Stickers desbloqueables con estrellitas
• 8 stickers diferentes con costos variados
• Pantalla especial "¡Puedes jugar!" cada 10 estrellitas
• Reinicio semanal automático
• Celebraciones aleatorias mejoradas
• Colección de stickers visualizable

Los stickers se desbloquean automáticamente
cuando el niño acumula suficientes estrellitas.
        """
        messagebox.showinfo("Sistema de Recompensas", info)
    
    def verificar_archivos(self):
        """Verifica que todos los archivos necesarios estén presentes"""
        archivos_necesarios = [
            'lector.py',
            'config.json',
            'preguntas.json',
            'panel_padres_mejorado.py',
            'sistema_recompensas_mejorado.py',
            'preguntas_expandidas.json'
        ]
        
        archivos_faltantes = []
        archivos_presentes = []
        
        for archivo in archivos_necesarios:
            if (self.directorio_base / archivo).exists():
                archivos_presentes.append(archivo)
            else:
                archivos_faltantes.append(archivo)
        
        mensaje = f"✅ Archivos presentes ({len(archivos_presentes)}):\n"
        mensaje += "\n".join(f"  • {archivo}" for archivo in archivos_presentes)
        
        if archivos_faltantes:
            mensaje += f"\n\n❌ Archivos faltantes ({len(archivos_faltantes)}):\n"
            mensaje += "\n".join(f"  • {archivo}" for archivo in archivos_faltantes)
        else:
            mensaje += "\n\n🎉 ¡Todos los archivos están presentes!"
        
        messagebox.showinfo("Verificación de Archivos", mensaje)
    
    def instalar_dependencias(self):
        """Instala las dependencias necesarias"""
        dependencias = ['matplotlib', 'numpy']
        
        mensaje = "Se instalarán las siguientes dependencias opcionales:\n\n"
        mensaje += "\n".join(f"• {dep}" for dep in dependencias)
        mensaje += "\n\nEstas son necesarias para los gráficos del panel de padres."
        mensaje += "\n¿Desea continuar?"
        
        if messagebox.askyesno("Instalar Dependencias", mensaje):
            try:
                for dep in dependencias:
                    subprocess.run([sys.executable, '-m', 'pip', 'install', dep], 
                                 check=True, capture_output=True)
                messagebox.showinfo("Éxito", "Dependencias instaladas correctamente")
            except subprocess.CalledProcessError as e:
                messagebox.showerror("Error", f"Error al instalar dependencias: {e}")
    
    def ejecutar(self):
        """Ejecuta la ventana de pruebas"""
        self.root.mainloop()

def main():
    """Función principal"""
    try:
        test = TestMejoras()
        test.ejecutar()
    except Exception as e:
        print(f"Error al iniciar pruebas: {e}")
        input("Presiona Enter para salir...")

if __name__ == "__main__":
    main()
