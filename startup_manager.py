#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gestor de Startup para Lector Progresivo
Maneja el registro en el startup de Windows y la detección de condiciones
"""

import os
import sys
import time
import winreg
import psutil
import threading
import subprocess
from pathlib import Path
from datetime import datetime, timedelta


class StartupManager:
    """Clase que maneja el startup automático y las condiciones de ejecución"""
    
    def __init__(self):
        self.app_name = "LectorProgresivo"
        self.exe_path = self._get_exe_path()
        self.registry_key = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"
        self.last_game_time = self._get_last_game_time()
        self.game_interval_hours = 2  # Cada 2 horas
        self.boot_delay_minutes = 5  # 5 minutos después del boot
        
    def _get_exe_path(self):
        """Obtiene la ruta del ejecutable actual"""
        if getattr(sys, 'frozen', False):
            # Si está ejecutándose como .exe
            return sys.executable
        else:
            # Si está ejecutándose como script Python
            return os.path.abspath(__file__)
    
    def _get_last_game_time(self):
        """Obtiene la última vez que se ejecutó el juego"""
        try:
            config_file = Path.home() / "AppData" / "Local" / "LectorProgresivo" / "last_game.txt"
            if config_file.exists():
                with open(config_file, 'r') as f:
                    timestamp = f.read().strip()
                    return datetime.fromisoformat(timestamp)
        except:
            pass
        return datetime.now() - timedelta(hours=3)  # Default: hace 3 horas
    
    def _save_last_game_time(self):
        """Guarda la hora actual como última vez que se ejecutó el juego"""
        try:
            config_dir = Path.home() / "AppData" / "Local" / "LectorProgresivo"
            config_dir.mkdir(parents=True, exist_ok=True)
            config_file = config_dir / "last_game.txt"
            
            with open(config_file, 'w') as f:
                f.write(datetime.now().isoformat())
        except Exception as e:
            print(f"Error guardando última vez de juego: {e}")
    
    def register_startup(self):
        """Registra la aplicación en el startup de Windows"""
        try:
            # Abrir clave de registro
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, self.registry_key, 0, winreg.KEY_SET_VALUE)
            
            # Crear comando con parámetros para startup
            startup_command = f'"{self.exe_path}" --startup'
            
            # Registrar en el startup
            winreg.SetValueEx(key, self.app_name, 0, winreg.REG_SZ, startup_command)
            winreg.CloseKey(key)
            
            print(f"✅ Aplicación registrada en startup: {startup_command}")
            return True
            
        except Exception as e:
            print(f"❌ Error registrando en startup: {e}")
            return False
    
    def unregister_startup(self):
        """Desregistra la aplicación del startup de Windows"""
        try:
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, self.registry_key, 0, winreg.KEY_SET_VALUE)
            winreg.DeleteValue(key, self.app_name)
            winreg.CloseKey(key)
            
            print("✅ Aplicación desregistrada del startup")
            return True
            
        except FileNotFoundError:
            print("ℹ️ La aplicación no estaba registrada en startup")
            return True
        except Exception as e:
            print(f"❌ Error desregistrando del startup: {e}")
            return False
    
    def is_registered_in_startup(self):
        """Verifica si la aplicación está registrada en el startup"""
        try:
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, self.registry_key, 0, winreg.KEY_READ)
            value, _ = winreg.QueryValueEx(key, self.app_name)
            winreg.CloseKey(key)
            return True
        except FileNotFoundError:
            return False
        except Exception:
            return False
    
    def has_keyboard_connected(self):
        """Verifica si hay un teclado conectado al sistema"""
        try:
            # Método 1: Verificar dispositivos HID
            import win32api
            import win32con
            
            # Buscar dispositivos de entrada
            devices = []
            try:
                # Enumerar dispositivos HID
                import win32file
                # Este es un método simplificado, en producción se usaría una librería más robusta
                return True  # Por ahora asumimos que hay teclado
            except:
                pass
            
            # Método 2: Verificar a través de WMI (más confiable)
            try:
                import wmi
                c = wmi.WMI()
                keyboards = c.Win32_Keyboard()
                return len(keyboards) > 0
            except:
                pass
            
            # Método 3: Fallback - asumir que hay teclado si no se puede verificar
            return True
            
        except Exception as e:
            print(f"Error verificando teclado: {e}")
            return True  # En caso de error, asumir que hay teclado
    
    def is_system_idle(self):
        """Verifica si el sistema está en idle (sin actividad del usuario)"""
        try:
            import win32api
            
            # Obtener tiempo de inactividad en milisegundos
            class LASTINPUTINFO:
                def __init__(self):
                    self.cbSize = 8
                    self.dwTime = 0
            
            lastInputInfo = LASTINPUTINFO()
            lastInputInfo.cbSize = 8
            
            if win32api.GetLastInputInfo(lastInputInfo):
                millis = win32api.GetTickCount() - lastInputInfo.dwTime
                seconds = millis / 1000.0
                minutes = seconds / 60.0
                
                # Considerar idle si no hay actividad por más de 10 minutos
                return minutes > 10
            
        except Exception as e:
            print(f"Error verificando idle: {e}")
            
        return False
    
    def should_launch_game(self):
        """Determina si se debe lanzar el juego basado en las condiciones"""
        now = datetime.now()
        time_since_last_game = now - self.last_game_time
        
        # Verificar si han pasado 2 horas desde el último juego
        hours_passed = time_since_last_game.total_seconds() / 3600
        
        conditions = {
            'keyboard_connected': self.has_keyboard_connected(),
            'time_elapsed': hours_passed >= self.game_interval_hours,
            'system_not_idle': not self.is_system_idle()
        }
        
        print(f"🔍 Condiciones para lanzar juego:")
        print(f"   - Teclado conectado: {conditions['keyboard_connected']}")
        print(f"   - Tiempo transcurrido ({hours_passed:.1f}h >= {self.game_interval_hours}h): {conditions['time_elapsed']}")
        print(f"   - Sistema no idle: {conditions['system_not_idle']}")
        
        # Todas las condiciones deben cumplirse
        should_launch = all(conditions.values())
        
        if should_launch:
            self._save_last_game_time()
            
        return should_launch
    
    def wait_after_boot(self):
        """Espera el tiempo especificado después del boot"""
        print(f"⏳ Esperando {self.boot_delay_minutes} minutos después del boot...")
        time.sleep(self.boot_delay_minutes * 60)
        print("✅ Tiempo de espera completado")
    
    def start_monitoring(self):
        """Inicia el monitoreo para lanzar el juego cada 2 horas"""
        print("🔄 Iniciando monitoreo para lanzar juego cada 2 horas...")
        
        while True:
            try:
                if self.should_launch_game():
                    print("🎮 Lanzando juego...")
                    self.launch_game()
                
                # Esperar 30 minutos antes de verificar de nuevo
                time.sleep(30 * 60)
                
            except KeyboardInterrupt:
                print("🛑 Monitoreo detenido por el usuario")
                break
            except Exception as e:
                print(f"❌ Error en monitoreo: {e}")
                time.sleep(60)  # Esperar 1 minuto antes de reintentar
    
    def launch_game(self):
        """Lanza el juego principal"""
        try:
            game_path = Path(self.exe_path).parent / "lector.exe"
            if not game_path.exists():
                # Si no existe el .exe, buscar el script Python
                game_path = Path(self.exe_path).parent / "lector.py"
                if game_path.exists():
                    subprocess.Popen([sys.executable, str(game_path)], 
                                   creationflags=subprocess.CREATE_NEW_CONSOLE)
                else:
                    print("❌ No se encontró el archivo del juego")
                    return
            else:
                subprocess.Popen([str(game_path)], 
                               creationflags=subprocess.CREATE_NEW_CONSOLE)
            
            print("✅ Juego lanzado exitosamente")
            
        except Exception as e:
            print(f"❌ Error lanzando juego: {e}")


def main():
    """Función principal del gestor de startup"""
    startup_manager = StartupManager()
    
    # Verificar argumentos de línea de comandos
    if len(sys.argv) > 1:
        if sys.argv[1] == "--startup":
            # Ejecutado desde startup
            print("🚀 Ejecutado desde startup del sistema")
            
            # Verificar si hay teclado conectado
            if not startup_manager.has_keyboard_connected():
                print("⚠️ No hay teclado conectado, saliendo...")
                return
            
            # Esperar después del boot
            startup_manager.wait_after_boot()
            
            # Iniciar monitoreo
            startup_manager.start_monitoring()
            
        elif sys.argv[1] == "--register":
            # Registrar en startup
            startup_manager.register_startup()
            
        elif sys.argv[1] == "--unregister":
            # Desregistrar del startup
            startup_manager.unregister_startup()
            
        elif sys.argv[1] == "--status":
            # Mostrar estado
            is_registered = startup_manager.is_registered_in_startup()
            print(f"Estado en startup: {'✅ Registrado' if is_registered else '❌ No registrado'}")
            
    else:
        print("Uso: startup_manager.py [--startup|--register|--unregister|--status]")


if __name__ == "__main__":
    main()
