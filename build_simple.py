#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script simplificado para crear el ejecutable del Lector Progresivo
Versión más robusta que maneja mejor los archivos de datos
"""

import os
import sys
import subprocess
from pathlib import Path


def install_requirements():
    """Instala las dependencias necesarias para crear el .exe"""
    requirements = [
        'pyinstaller',
        'psutil',
        'pywin32'
    ]
    
    print("📦 Instalando dependencias para crear .exe...")
    for req in requirements:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', req])
            print(f"✅ {req} instalado")
        except subprocess.CalledProcessError:
            print(f"❌ Error instalando {req}")
            return False
    
    return True


def build_main_executable():
    """Construye el ejecutable principal del juego"""
    print("🔨 Construyendo ejecutable principal...")
    
    # Buscar archivos de datos
    data_files = []
    
    # Archivos JSON requeridos
    json_files = ['config.json', 'preguntas.json', 'recompensas.json']
    for json_file in json_files:
        if Path(json_file).exists():
            data_files.extend(['--add-data', f'{json_file};.'])
    
    # Archivos Python adicionales
    python_files = ['sistema_adaptativo.py', 'sistema_recompensas_mejorado.py']
    for py_file in python_files:
        if Path(py_file).exists():
            data_files.extend(['--add-data', f'{py_file};.'])
    
    # Comando PyInstaller para el ejecutable principal
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onedir',  # Crear directorio en lugar de un solo archivo
        '--windowed',  # Sin ventana de consola
        '--name', 'LectorProgresivo',
        '--clean',
        '--noconfirm',
        '--hidden-import', 'tkinter',
        '--hidden-import', 'tkinter.ttk',
        '--hidden-import', 'tkinter.messagebox',
        '--hidden-import', 'winsound',
        '--hidden-import', 'json',
        '--hidden-import', 'random',
        '--hidden-import', 'time',
        '--hidden-import', 'threading',
        '--hidden-import', 'pathlib',
        '--hidden-import', 'datetime'
    ]
    
    # Agregar archivos de datos
    cmd.extend(data_files)
    
    # Agregar archivo principal
    cmd.append('lector.py')
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Ejecutable principal creado exitosamente")
            return True
        else:
            print("❌ Error creando ejecutable principal:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error durante la construcción: {e}")
        return False


def build_startup_executable():
    """Construye el ejecutable del gestor de startup"""
    print("🔨 Construyendo ejecutable de startup...")
    
    # Comando PyInstaller para el gestor de startup
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',  # Un solo archivo
        '--console',  # Con ventana de consola
        '--name', 'LectorProgresivo_Startup',
        '--clean',
        '--noconfirm',
        '--hidden-import', 'psutil',
        '--hidden-import', 'winreg',
        '--hidden-import', 'threading',
        '--hidden-import', 'subprocess',
        '--hidden-import', 'pathlib',
        '--hidden-import', 'datetime',
        '--hidden-import', 'time',
        'startup_manager.py'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Ejecutable de startup creado exitosamente")
            return True
        else:
            print("❌ Error creando ejecutable de startup:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error durante la construcción: {e}")
        return False


def copy_files_to_dist():
    """Copia archivos necesarios a la carpeta dist"""
    print("📁 Copiando archivos a dist...")
    
    dist_main = Path('dist/LectorProgresivo')
    dist_startup = Path('dist')
    
    # Archivos a copiar al directorio principal
    files_to_copy = ['config.json', 'preguntas.json', 'recompensas.json']
    
    for file_name in files_to_copy:
        src = Path(file_name)
        if src.exists():
            try:
                import shutil
                # Copiar al directorio del ejecutable principal
                if dist_main.exists():
                    shutil.copy2(src, dist_main / file_name)
                    print(f"✅ {file_name} copiado al directorio principal")
            except Exception as e:
                print(f"⚠️ Error copiando {file_name}: {e}")


def create_installer_script():
    """Crea un script de instalación simplificado"""
    installer_content = '''@echo off
echo ========================================
echo    Instalador de Lector Progresivo
echo ========================================
echo.

REM Verificar permisos de administrador
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Ejecutando con permisos de administrador
) else (
    echo ⚠️  Se recomienda ejecutar como administrador
    echo    para registrar correctamente en el startup
    pause
)

echo 📁 Creando directorio de instalación...
if not exist "%PROGRAMFILES%\\LectorProgresivo" mkdir "%PROGRAMFILES%\\LectorProgresivo"

echo 📁 Copiando archivos...
xcopy "LectorProgresivo\\*" "%PROGRAMFILES%\\LectorProgresivo\\" /E /I /Y >nul
copy "LectorProgresivo_Startup.exe" "%PROGRAMFILES%\\LectorProgresivo\\" >nul

echo 🔧 Registrando en startup...
"%PROGRAMFILES%\\LectorProgresivo\\LectorProgresivo_Startup.exe" --register

echo ✅ Instalación completada
echo.
echo 🎮 Para ejecutar el juego manualmente:
echo    "%PROGRAMFILES%\\LectorProgresivo\\LectorProgresivo.exe"
echo.
echo 🔄 El juego se ejecutará automáticamente:
echo    - Al iniciar el sistema (después de 5 minutos)
echo    - Cada 2 horas si el sistema no está idle
echo.
echo 🛑 Para desinstalar:
echo    "%PROGRAMFILES%\\LectorProgresivo\\LectorProgresivo_Startup.exe" --unregister
echo.
pause
'''
    
    with open('dist/instalar.bat', 'w', encoding='utf-8') as f:
        f.write(installer_content)
    
    print("✅ Script de instalación creado: dist/instalar.bat")


def main():
    """Función principal del script de construcción simplificado"""
    print("🚀 Iniciando proceso de construcción simplificado")
    print("=" * 50)
    
    # Verificar que estamos en el directorio correcto
    if not Path('lector.py').exists():
        print("❌ Error: No se encontró lector.py en el directorio actual")
        print("   Asegúrate de ejecutar este script desde el directorio del proyecto")
        return
    
    if not Path('startup_manager.py').exists():
        print("❌ Error: No se encontró startup_manager.py en el directorio actual")
        return
    
    # Instalar dependencias
    if not install_requirements():
        print("❌ Error instalando dependencias")
        return
    
    # Construir ejecutables
    if not build_main_executable():
        print("❌ Error construyendo ejecutable principal")
        return
    
    if not build_startup_executable():
        print("❌ Error construyendo ejecutable de startup")
        return
    
    # Copiar archivos adicionales
    copy_files_to_dist()
    
    # Crear script de instalación
    create_installer_script()
    
    print("\n" + "=" * 50)
    print("🎉 ¡Proceso completado exitosamente!")
    print("\n📋 Archivos generados en dist/:")
    
    dist_path = Path('dist')
    if dist_path.exists():
        for item in dist_path.iterdir():
            if item.is_file():
                print(f"   📄 {item.name}")
            elif item.is_dir():
                print(f"   📁 {item.name}/")
    
    print("\n📋 Próximos pasos:")
    print("1. Ve a la carpeta 'dist/'")
    print("2. Ejecuta 'instalar.bat' como administrador")
    print("3. El juego se registrará automáticamente en el startup")
    print("\n🎮 ¡Disfruta del Lector Progresivo!")


if __name__ == "__main__":
    main()
