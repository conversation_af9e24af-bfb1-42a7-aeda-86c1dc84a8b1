# UTF-8
#
# Para más detalles sobre fixed file info 'ffi' ver:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Lector Progresivo'),
        StringStruct(u'FileDescription', u'Asistente de Aprendizaje de Lectura'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'LectorProgresivo'),
        StringStruct(u'LegalCopyright', u'Copyright © 2024'),
        StringStruct(u'OriginalFilename', u'LectorProgresivo.exe'),
        StringStruct(u'ProductName', u'Lector Progresivo'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)