#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sistema Adaptativo de Aprendizaje
Maneja la lógica para adaptar las preguntas según el rendimiento del usuario
"""

import json
import random
import datetime
from typing import Dict, List, Optional, Tuple


class SistemaAdaptativo:
    """Clase que maneja la adaptación del contenido según el progreso del usuario"""
    
    def __init__(self, config: Dict, preguntas_data: Dict, progreso: Dict):
        self.config = config
        self.preguntas_data = preguntas_data
        self.progreso = progreso
        
    def obtener_pregunta_optima(self) -> Optional[Dict]:
        """
        Obtiene la pregunta más apropiada según el estado actual del usuario
        Considera: nivel actual, preguntas falladas, tiempo desde última vez, etc.
        """
        nivel_actual = self.progreso['usuario']['nivel_actual']
        
        # 1. Verificar si hay preguntas falladas que necesitan repaso
        pregunta_repaso = self._obtener_pregunta_repaso()
        if pregunta_repaso:
            return pregunta_repaso
            
        # 2. Obtener preguntas del nivel actual
        preguntas_nivel = self._obtener_preguntas_por_nivel(nivel_actual)
        
        # 3. Si no hay preguntas del nivel actual, bajar un nivel
        if not preguntas_nivel and nivel_actual > 1:
            preguntas_nivel = self._obtener_preguntas_por_nivel(nivel_actual - 1)
            
        # 4. Filtrar preguntas ya respondidas recientemente
        preguntas_filtradas = self._filtrar_preguntas_recientes(preguntas_nivel)
        
        # 5. Seleccionar pregunta con peso según dificultad
        return self._seleccionar_pregunta_ponderada(preguntas_filtradas or preguntas_nivel)
    
    def _obtener_pregunta_repaso(self) -> Optional[Dict]:
        """Obtiene una pregunta que necesita repaso por haber sido fallada"""
        preguntas_falladas = self.progreso.get('preguntas_falladas', {})
        umbral_repaso = self.config['aprendizaje']['repetir_falladas_despues_de']
        
        candidatas = []
        for pregunta_id, fallos in preguntas_falladas.items():
            if fallos >= umbral_repaso:
                pregunta = self._buscar_pregunta_por_id(int(pregunta_id))
                if pregunta:
                    # Dar más peso a preguntas con más fallos
                    peso = min(fallos, 5)  # Máximo peso de 5
                    candidatas.extend([pregunta] * peso)
        
        return random.choice(candidatas) if candidatas else None
    
    def _obtener_preguntas_por_nivel(self, nivel: int) -> List[Dict]:
        """Obtiene todas las preguntas de un nivel específico"""
        preguntas = []
        for nivel_key, nivel_data in self.preguntas_data['niveles'].items():
            for pregunta in nivel_data['preguntas']:
                if pregunta['dificultad'] == nivel:
                    preguntas.append(pregunta)
        return preguntas
    
    def _filtrar_preguntas_recientes(self, preguntas: List[Dict]) -> List[Dict]:
        """Filtra preguntas que fueron respondidas recientemente"""
        if not self.progreso['historial_respuestas']:
            return preguntas
            
        # Obtener IDs de preguntas respondidas en las últimas 5 respuestas
        ultimas_respuestas = self.progreso['historial_respuestas'][-5:]
        ids_recientes = {resp['pregunta_id'] for resp in ultimas_respuestas}
        
        # Filtrar preguntas no recientes
        preguntas_filtradas = [p for p in preguntas if p['id'] not in ids_recientes]
        
        return preguntas_filtradas
    
    def _seleccionar_pregunta_ponderada(self, preguntas: List[Dict]) -> Optional[Dict]:
        """Selecciona una pregunta usando pesos según diferentes factores"""
        if not preguntas:
            return None
            
        # Calcular pesos para cada pregunta
        preguntas_con_peso = []
        for pregunta in preguntas:
            peso = self._calcular_peso_pregunta(pregunta)
            preguntas_con_peso.append((pregunta, peso))
        
        # Selección ponderada
        total_peso = sum(peso for _, peso in preguntas_con_peso)
        if total_peso == 0:
            return random.choice(preguntas)
            
        punto_aleatorio = random.uniform(0, total_peso)
        peso_acumulado = 0
        
        for pregunta, peso in preguntas_con_peso:
            peso_acumulado += peso
            if peso_acumulado >= punto_aleatorio:
                return pregunta
                
        return preguntas[-1]  # Fallback
    
    def _calcular_peso_pregunta(self, pregunta: Dict) -> float:
        """Calcula el peso de una pregunta según varios factores"""
        peso_base = 1.0
        pregunta_id = str(pregunta['id'])
        
        # Factor 1: Preguntas nunca respondidas tienen más peso
        if not self._pregunta_fue_respondida(pregunta['id']):
            peso_base *= 2.0
            
        # Factor 2: Preguntas falladas tienen más peso
        fallos = self.progreso.get('preguntas_falladas', {}).get(pregunta_id, 0)
        if fallos > 0:
            peso_base *= (1.0 + fallos * 0.3)
            
        # Factor 3: Tiempo desde última vez que se respondió
        tiempo_factor = self._calcular_factor_tiempo(pregunta['id'])
        peso_base *= tiempo_factor
        
        return peso_base
    
    def _pregunta_fue_respondida(self, pregunta_id: int) -> bool:
        """Verifica si una pregunta ya fue respondida alguna vez"""
        return any(resp['pregunta_id'] == pregunta_id 
                  for resp in self.progreso['historial_respuestas'])
    
    def _calcular_factor_tiempo(self, pregunta_id: int) -> float:
        """Calcula un factor basado en cuánto tiempo pasó desde la última respuesta"""
        # Buscar la última vez que se respondió esta pregunta
        ultima_respuesta = None
        for resp in reversed(self.progreso['historial_respuestas']):
            if resp['pregunta_id'] == pregunta_id:
                ultima_respuesta = resp
                break
                
        if not ultima_respuesta:
            return 1.0  # Nunca respondida
            
        try:
            fecha_ultima = datetime.datetime.fromisoformat(ultima_respuesta['fecha'])
            tiempo_transcurrido = datetime.datetime.now() - fecha_ultima
            dias_transcurridos = tiempo_transcurrido.days
            
            # Más tiempo = más peso (hasta un máximo)
            factor = min(1.0 + dias_transcurridos * 0.1, 2.0)
            return factor
        except:
            return 1.0
    
    def _buscar_pregunta_por_id(self, pregunta_id: int) -> Optional[Dict]:
        """Busca una pregunta específica por su ID"""
        for nivel_key, nivel_data in self.preguntas_data['niveles'].items():
            for pregunta in nivel_data['preguntas']:
                if pregunta['id'] == pregunta_id:
                    return pregunta
        return None
    
    def evaluar_progreso_nivel(self) -> Tuple[bool, int]:
        """
        Evalúa si el usuario debe subir o bajar de nivel
        Retorna: (debe_cambiar_nivel, nuevo_nivel)
        """
        nivel_actual = self.progreso['usuario']['nivel_actual']
        stats_nivel = self.progreso['estadisticas']['preguntas_por_nivel'].get(str(nivel_actual), {})
        
        correctas = stats_nivel.get('correctas', 0)
        incorrectas = stats_nivel.get('incorrectas', 0)
        total = correctas + incorrectas
        
        if total < 3:  # Muy pocas preguntas para evaluar
            return False, nivel_actual
            
        tasa_acierto = correctas / total
        
        # Subir de nivel si tiene alta tasa de acierto
        if (tasa_acierto >= 0.8 and 
            correctas >= self.config['aprendizaje']['preguntas_para_subir_nivel'] and
            nivel_actual < 4):
            return True, nivel_actual + 1
            
        # Bajar de nivel si tiene baja tasa de acierto
        if tasa_acierto <= 0.3 and total >= 5 and nivel_actual > 1:
            return True, nivel_actual - 1
            
        return False, nivel_actual
    
    def obtener_sugerencia_ayuda(self, pregunta: Dict, intentos_fallidos: int) -> Optional[str]:
        """Obtiene una sugerencia de ayuda para una pregunta específica"""
        if intentos_fallidos < self.config['aprendizaje']['mostrar_ayuda_visual_despues_de']:
            return None
            
        # Generar ayuda según el tipo de pregunta
        respuesta = pregunta['respuesta_esperada'].lower()
        
        if intentos_fallidos == 2:
            # Primera ayuda: número de letras
            return f"💡 Pista: La respuesta tiene {len(respuesta)} letras"
        elif intentos_fallidos == 3:
            # Segunda ayuda: primera letra
            return f"💡 Pista: Empieza con la letra '{respuesta[0].upper()}'"
        elif intentos_fallidos >= 4:
            # Ayuda final: mostrar con guiones
            ayuda = ""
            for i, letra in enumerate(respuesta):
                if i == 0 or i == len(respuesta) - 1:
                    ayuda += letra.upper()
                else:
                    ayuda += "_"
            return f"💡 Pista: {ayuda}"
            
        return None
    
    def generar_reporte_progreso(self) -> Dict:
        """Genera un reporte detallado del progreso del usuario"""
        stats = self.progreso['estadisticas']
        usuario = self.progreso['usuario']
        
        # Calcular estadísticas generales
        total_preguntas = usuario['total_preguntas_respondidas']
        total_correctas = usuario['total_respuestas_correctas']
        tasa_acierto_general = (total_correctas / total_preguntas * 100) if total_preguntas > 0 else 0
        
        # Estadísticas por nivel
        stats_por_nivel = {}
        for nivel, datos in stats['preguntas_por_nivel'].items():
            correctas = datos['correctas']
            incorrectas = datos['incorrectas']
            total_nivel = correctas + incorrectas
            tasa_nivel = (correctas / total_nivel * 100) if total_nivel > 0 else 0
            
            stats_por_nivel[nivel] = {
                'total_preguntas': total_nivel,
                'correctas': correctas,
                'incorrectas': incorrectas,
                'tasa_acierto': round(tasa_nivel, 1)
            }
        
        # Preguntas más difíciles
        preguntas_dificiles = []
        for pregunta_id, fallos in self.progreso.get('preguntas_falladas', {}).items():
            if fallos >= 2:
                pregunta = self._buscar_pregunta_por_id(int(pregunta_id))
                if pregunta:
                    preguntas_dificiles.append({
                        'pregunta': pregunta['pregunta'],
                        'respuesta': pregunta['respuesta_esperada'],
                        'fallos': fallos
                    })
        
        return {
            'resumen_general': {
                'nivel_actual': usuario['nivel_actual'],
                'estrellitas': usuario['estrellitas_acumuladas'],
                'total_preguntas': total_preguntas,
                'tasa_acierto_general': round(tasa_acierto_general, 1),
                'mejor_racha': stats['mejor_racha'],
                'racha_actual': stats['racha_actual_correctas']
            },
            'estadisticas_por_nivel': stats_por_nivel,
            'preguntas_dificiles': sorted(preguntas_dificiles, key=lambda x: x['fallos'], reverse=True)[:5],
            'fecha_reporte': datetime.datetime.now().isoformat()
        }
