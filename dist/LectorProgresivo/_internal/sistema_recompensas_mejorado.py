#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sistema de Recompensas Mejorado
Maneja stickers virtuales, celebraciones y reinicio semanal
"""

import tkinter as tk
from tkinter import ttk
import json
import datetime
from pathlib import Path
import random

class SistemaRecompensas:
    def __init__(self, parent_app):
        self.parent_app = parent_app
        self.directorio_base = Path(__file__).parent
        self.cargar_recompensas()
        
    def cargar_recompensas(self):
        """Carga el estado de las recompensas"""
        try:
            with open(self.directorio_base / 'recompensas.json', 'r', encoding='utf-8') as f:
                self.recompensas_data = json.load(f)
        except FileNotFoundError:
            # Crear archivo de recompensas por defecto
            self.recompensas_data = {
                "stickers_desbloqueados": [],
                "fecha_ultimo_reinicio": "",
                "celebraciones_disponibles": [
                    "¡Excelente trabajo! 🌟",
                    "¡Eres increíble! 🎉",
                    "¡Sigue así! 💪",
                    "¡Fantástico! 🎊",
                    "¡Muy bien! 👏",
                    "¡Genial! ⭐",
                    "¡Perfecto! 🏆",
                    "¡Súper! 🚀"
                ],
                "stickers_disponibles": [
                    {"id": 1, "nombre": "Estrella Dorada", "emoji": "⭐", "costo": 5, "desbloqueado": False},
                    {"id": 2, "nombre": "Corazón", "emoji": "❤️", "costo": 3, "desbloqueado": False},
                    {"id": 3, "nombre": "Trofeo", "emoji": "🏆", "costo": 10, "desbloqueado": False},
                    {"id": 4, "nombre": "Cohete", "emoji": "🚀", "costo": 8, "desbloqueado": False},
                    {"id": 5, "nombre": "Arcoíris", "emoji": "🌈", "costo": 6, "desbloqueado": False},
                    {"id": 6, "nombre": "Unicornio", "emoji": "🦄", "costo": 15, "desbloqueado": False},
                    {"id": 7, "nombre": "Corona", "emoji": "👑", "costo": 12, "desbloqueado": False},
                    {"id": 8, "nombre": "Diamante", "emoji": "💎", "costo": 20, "desbloqueado": False}
                ]
            }
            self.guardar_recompensas()
    
    def guardar_recompensas(self):
        """Guarda el estado de las recompensas"""
        try:
            with open(self.directorio_base / 'recompensas.json', 'w', encoding='utf-8') as f:
                json.dump(self.recompensas_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error al guardar recompensas: {e}")
    
    def verificar_reinicio_semanal(self):
        """Verifica si es necesario reiniciar las estrellitas semanalmente"""
        ahora = datetime.datetime.now()
        fecha_ultimo_reinicio = self.recompensas_data.get('fecha_ultimo_reinicio', '')
        
        if fecha_ultimo_reinicio:
            ultimo_reinicio = datetime.datetime.fromisoformat(fecha_ultimo_reinicio)
            dias_transcurridos = (ahora - ultimo_reinicio).days
            
            if dias_transcurridos >= 7:  # Una semana
                self.reiniciar_estrellitas_semanal()
        else:
            # Primera vez, establecer fecha
            self.recompensas_data['fecha_ultimo_reinicio'] = ahora.isoformat()
            self.guardar_recompensas()
    
    def reiniciar_estrellitas_semanal(self):
        """Reinicia las estrellitas semanalmente"""
        # Resetear estrellitas pero mantener stickers desbloqueados
        self.parent_app.progreso['usuario']['estrellitas_acumuladas'] = 0
        self.recompensas_data['fecha_ultimo_reinicio'] = datetime.datetime.now().isoformat()
        
        # Mostrar mensaje de reinicio
        self.mostrar_mensaje_reinicio()
        
        self.guardar_recompensas()
        self.parent_app.guardar_progreso()
    
    def mostrar_mensaje_reinicio(self):
        """Muestra mensaje de reinicio semanal"""
        mensaje = "🎊 ¡Nueva semana, nuevas aventuras!\n\nTus estrellitas se han reiniciado,\n¡pero tus stickers siguen contigo!\n\n¡A por más estrellitas! ⭐"
        self.parent_app.mostrar_mensaje_temporal(mensaje, color='#FFD700')
    
    def verificar_nuevos_stickers(self, estrellitas_actuales):
        """Verifica si se pueden desbloquear nuevos stickers"""
        stickers_nuevos = []
        
        for sticker in self.recompensas_data['stickers_disponibles']:
            if not sticker['desbloqueado'] and estrellitas_actuales >= sticker['costo']:
                sticker['desbloqueado'] = True
                stickers_nuevos.append(sticker)
        
        if stickers_nuevos:
            self.mostrar_stickers_desbloqueados(stickers_nuevos)
            self.guardar_recompensas()
    
    def mostrar_stickers_desbloqueados(self, stickers_nuevos):
        """Muestra los stickers recién desbloqueados"""
        for sticker in stickers_nuevos:
            # Reproducir sonido especial de sticker
            self.parent_app.reproducir_sonido_especial("sticker")
            mensaje = f"🎉 ¡Nuevo sticker desbloqueado!\n\n{sticker['emoji']} {sticker['nombre']}\n\n¡Felicidades!"
            self.parent_app.mostrar_celebracion(mensaje)
    
    def obtener_celebracion_aleatoria(self):
        """Obtiene un mensaje de celebración aleatorio"""
        celebraciones = self.recompensas_data['celebraciones_disponibles']
        return random.choice(celebraciones)
    
    def mostrar_pantalla_juego_desbloqueado(self):
        """Muestra la pantalla de 'Puedes jugar'"""
        # Reproducir sonido de celebración especial
        self.parent_app.reproducir_sonido_especial("celebracion")

        # Crear ventana de celebración especial
        ventana_juego = tk.Toplevel(self.parent_app.root)
        ventana_juego.title("¡Tiempo de Juego!")
        ventana_juego.geometry("600x400")
        ventana_juego.configure(bg='#FFD700')
        ventana_juego.attributes('-topmost', True)
        
        # Centrar ventana
        ventana_juego.transient(self.parent_app.root)
        ventana_juego.grab_set()
        
        # Contenido
        tk.Label(ventana_juego, 
                text="🎮 ¡PUEDES JUGAR! 🎮", 
                font=('Comic Sans MS', 32, 'bold'),
                fg='#FF6B6B',
                bg='#FFD700').pack(pady=30)
        
        tk.Label(ventana_juego, 
                text="¡Has ganado tiempo de juego!\n\nTe has portado muy bien\naprendiendo a leer.", 
                font=('Comic Sans MS', 16),
                fg='#2F4F4F',
                bg='#FFD700',
                justify='center').pack(pady=20)
        
        # Mostrar stickers desbloqueados
        stickers_frame = tk.Frame(ventana_juego, bg='#FFD700')
        stickers_frame.pack(pady=20)
        
        stickers_desbloqueados = [s for s in self.recompensas_data['stickers_disponibles'] if s['desbloqueado']]
        for i, sticker in enumerate(stickers_desbloqueados[:6]):  # Mostrar máximo 6
            tk.Label(stickers_frame, 
                    text=sticker['emoji'], 
                    font=('Arial', 24),
                    bg='#FFD700').grid(row=0, column=i, padx=10)
        
        # Botón para cerrar
        tk.Button(ventana_juego,
                 text="¡Entendido!",
                 font=('Comic Sans MS', 16, 'bold'),
                 bg='#90EE90',
                 fg='#2F4F4F',
                 padx=30,
                 pady=10,
                 command=ventana_juego.destroy).pack(pady=30)
    
    def mostrar_coleccion_stickers(self):
        """Muestra la colección completa de stickers"""
        ventana_coleccion = tk.Toplevel(self.parent_app.root)
        ventana_coleccion.title("Mi Colección de Stickers")
        ventana_coleccion.geometry("800x600")
        ventana_coleccion.configure(bg='#F0F8FF')
        
        # Título
        tk.Label(ventana_coleccion, 
                text="🌟 Mi Colección de Stickers 🌟", 
                font=('Comic Sans MS', 20, 'bold'),
                fg='#4169E1',
                bg='#F0F8FF').pack(pady=20)
        
        # Frame para stickers
        canvas = tk.Canvas(ventana_coleccion, bg='#F0F8FF')
        scrollbar = ttk.Scrollbar(ventana_coleccion, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Mostrar stickers en grid
        row = 0
        col = 0
        for sticker in self.recompensas_data['stickers_disponibles']:
            sticker_frame = tk.Frame(scrollable_frame, bg='white', relief='raised', bd=2)
            sticker_frame.grid(row=row, column=col, padx=10, pady=10, sticky='nsew')
            
            if sticker['desbloqueado']:
                # Sticker desbloqueado
                tk.Label(sticker_frame, 
                        text=sticker['emoji'], 
                        font=('Arial', 40),
                        bg='white').pack(pady=10)
                tk.Label(sticker_frame, 
                        text=sticker['nombre'], 
                        font=('Arial', 10, 'bold'),
                        bg='white').pack()
                tk.Label(sticker_frame, 
                        text="✓ Desbloqueado", 
                        font=('Arial', 8),
                        fg='green',
                        bg='white').pack(pady=5)
            else:
                # Sticker bloqueado
                tk.Label(sticker_frame, 
                        text="🔒", 
                        font=('Arial', 40),
                        bg='white').pack(pady=10)
                tk.Label(sticker_frame, 
                        text="???", 
                        font=('Arial', 10, 'bold'),
                        bg='white').pack()
                tk.Label(sticker_frame, 
                        text=f"Necesitas {sticker['costo']} ⭐", 
                        font=('Arial', 8),
                        fg='orange',
                        bg='white').pack(pady=5)
            
            col += 1
            if col >= 4:  # 4 columnas
                col = 0
                row += 1
        
        canvas.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y", pady=10)
        
        # Botón para cerrar
        tk.Button(ventana_coleccion,
                 text="Cerrar",
                 font=('Arial', 12),
                 command=ventana_coleccion.destroy).pack(pady=10)
    
    def verificar_celebracion_especial(self, estrellitas):
        """Verifica si se debe mostrar una celebración especial"""
        # Cada 10 estrellitas, mostrar pantalla de juego
        if estrellitas > 0 and estrellitas % 10 == 0:
            self.mostrar_pantalla_juego_desbloqueado()
        
        # Verificar nuevos stickers
        self.verificar_nuevos_stickers(estrellitas)
        
        # Verificar reinicio semanal
        self.verificar_reinicio_semanal()
