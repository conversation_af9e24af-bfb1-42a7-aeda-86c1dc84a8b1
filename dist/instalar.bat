@echo off
echo ========================================
echo    Instalador de Lector Progresivo
echo ========================================
echo.

REM Verificar permisos de administrador
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Ejecutando con permisos de administrador
) else (
    echo ⚠️  Se recomienda ejecutar como administrador
    echo    para registrar correctamente en el startup
    pause
)

echo 📁 Creando directorio de instalación...
if not exist "%PROGRAMFILES%\LectorProgresivo" mkdir "%PROGRAMFILES%\LectorProgresivo"

echo 📁 Copiando archivos...
xcopy "LectorProgresivo\*" "%PROGRAMFILES%\LectorProgresivo\" /E /I /Y >nul
copy "LectorProgresivo_Startup.exe" "%PROGRAMFILES%\LectorProgresivo\" >nul

echo 🔧 Registrando en startup...
"%PROGRAMFILES%\LectorProgresivo\LectorProgresivo_Startup.exe" --register

echo ✅ Instalación completada
echo.
echo 🎮 Para ejecutar el juego manualmente:
echo    "%PROGRAMFILES%\LectorProgresivo\LectorProgresivo.exe"
echo.
echo 🔄 El juego se ejecutará automáticamente:
echo    - Al iniciar el sistema (después de 5 minutos)
echo    - Cada 2 horas si el sistema no está idle
echo.
echo 🛑 Para desinstalar:
echo    "%PROGRAMFILES%\LectorProgresivo\LectorProgresivo_Startup.exe" --unregister
echo.
pause
