#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Panel de Seguimiento para Padres
Permite a los padres ver el progreso detallado de sus hijos
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import datetime
from pathlib import Path
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np

class PanelPadres:
    def __init__(self):
        self.directorio_base = Path(__file__).parent
        self.cargar_datos()
        self.crear_ventana()
        
    def cargar_datos(self):
        """Carga los datos de progreso del niño"""
        try:
            with open(self.directorio_base / 'respuestas_guardadas.json', 'r', encoding='utf-8') as f:
                self.datos = json.load(f)
        except FileNotFoundError:
            messagebox.showerror("Error", "No se encontraron datos de progreso")
            return
            
    def crear_ventana(self):
        """Crea la ventana principal del panel"""
        self.root = tk.Tk()
        self.root.title("Panel de Seguimiento para Padres")
        self.root.geometry("1200x800")
        self.root.configure(bg='#F0F8FF')
        
        # Crear notebook para pestañas
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Crear pestañas
        self.crear_pestaña_resumen()
        self.crear_pestaña_historial()
        self.crear_pestaña_estadisticas()
        self.crear_pestaña_preguntas_dificiles()
        
    def crear_pestaña_resumen(self):
        """Crea la pestaña de resumen general"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="📊 Resumen")
        
        # Información general
        info_frame = tk.LabelFrame(frame, text="Información General", font=('Arial', 12, 'bold'))
        info_frame.pack(fill='x', padx=10, pady=10)
        
        usuario = self.datos.get('usuario', {})
        
        tk.Label(info_frame, text=f"Nivel Actual: {usuario.get('nivel_actual', 1)}", 
                font=('Arial', 11)).pack(anchor='w', padx=10, pady=2)
        tk.Label(info_frame, text=f"Estrellitas Acumuladas: {usuario.get('estrellitas_acumuladas', 0)}", 
                font=('Arial', 11)).pack(anchor='w', padx=10, pady=2)
        tk.Label(info_frame, text=f"Total Preguntas Respondidas: {usuario.get('total_preguntas_respondidas', 0)}", 
                font=('Arial', 11)).pack(anchor='w', padx=10, pady=2)
        tk.Label(info_frame, text=f"Total Respuestas Correctas: {usuario.get('total_respuestas_correctas', 0)}", 
                font=('Arial', 11)).pack(anchor='w', padx=10, pady=2)
        
        # Calcular porcentaje de aciertos
        total_preguntas = usuario.get('total_preguntas_respondidas', 0)
        total_correctas = usuario.get('total_respuestas_correctas', 0)
        porcentaje = (total_correctas / total_preguntas * 100) if total_preguntas > 0 else 0
        
        tk.Label(info_frame, text=f"Porcentaje de Aciertos: {porcentaje:.1f}%", 
                font=('Arial', 11, 'bold'), fg='green' if porcentaje >= 70 else 'orange').pack(anchor='w', padx=10, pady=2)
        
        # Última sesión
        ultima_sesion = usuario.get('fecha_ultima_sesion', '')
        if ultima_sesion:
            fecha = datetime.datetime.fromisoformat(ultima_sesion).strftime("%d/%m/%Y %H:%M")
            tk.Label(info_frame, text=f"Última Sesión: {fecha}", 
                    font=('Arial', 11)).pack(anchor='w', padx=10, pady=2)
        
        # Estadísticas por nivel
        stats_frame = tk.LabelFrame(frame, text="Rendimiento por Nivel", font=('Arial', 12, 'bold'))
        stats_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.crear_grafico_niveles(stats_frame)
        
    def crear_pestaña_historial(self):
        """Crea la pestaña de historial de respuestas"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="📝 Historial")
        
        # Crear treeview para mostrar historial
        columns = ('Fecha', 'Pregunta', 'Respuesta Usuario', 'Correcta', 'Tiempo', 'Nivel')
        tree = ttk.Treeview(frame, columns=columns, show='headings', height=20)
        
        # Configurar columnas
        tree.heading('Fecha', text='Fecha')
        tree.heading('Pregunta', text='Pregunta')
        tree.heading('Respuesta Usuario', text='Respuesta Usuario')
        tree.heading('Correcta', text='¿Correcta?')
        tree.heading('Tiempo', text='Tiempo (s)')
        tree.heading('Nivel', text='Nivel')
        
        tree.column('Fecha', width=120)
        tree.column('Pregunta', width=300)
        tree.column('Respuesta Usuario', width=150)
        tree.column('Correcta', width=80)
        tree.column('Tiempo', width=80)
        tree.column('Nivel', width=60)
        
        # Agregar scrollbar
        scrollbar = ttk.Scrollbar(frame, orient='vertical', command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        # Llenar con datos (últimas 50 respuestas)
        historial = self.datos.get('historial_respuestas', [])
        for respuesta in historial[-50:]:  # Últimas 50
            fecha = datetime.datetime.fromisoformat(respuesta['fecha']).strftime("%d/%m %H:%M")
            correcta = "✓" if respuesta['es_correcta'] else "✗"
            tree.insert('', 'end', values=(
                fecha,
                respuesta['pregunta'][:50] + "..." if len(respuesta['pregunta']) > 50 else respuesta['pregunta'],
                respuesta['respuesta_usuario'],
                correcta,
                f"{respuesta['tiempo_respuesta_segundos']:.1f}",
                respuesta['nivel']
            ))
        
        tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        scrollbar.pack(side='right', fill='y', pady=10)
        
    def crear_pestaña_estadisticas(self):
        """Crea la pestaña de estadísticas avanzadas"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="📈 Estadísticas")
        
        # Calcular estadísticas
        self.calcular_estadisticas_avanzadas(frame)
        
    def crear_pestaña_preguntas_dificiles(self):
        """Crea la pestaña de preguntas que más se fallan"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="❌ Preguntas Difíciles")
        
        preguntas_frame = tk.LabelFrame(frame, text="Preguntas Más Falladas", font=('Arial', 12, 'bold'))
        preguntas_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Crear treeview para preguntas falladas
        columns = ('Pregunta', 'Veces Fallada', 'Última Vez')
        tree = ttk.Treeview(preguntas_frame, columns=columns, show='headings', height=15)
        
        tree.heading('Pregunta', text='Pregunta')
        tree.heading('Veces Fallada', text='Veces Fallada')
        tree.heading('Última Vez', text='Última Vez')
        
        tree.column('Pregunta', width=400)
        tree.column('Veces Fallada', width=120)
        tree.column('Última Vez', width=120)
        
        # Llenar con preguntas falladas
        preguntas_falladas = self.datos.get('preguntas_falladas', {})
        historial = self.datos.get('historial_respuestas', [])
        
        # Crear diccionario de preguntas por ID
        preguntas_por_id = {}
        for respuesta in historial:
            if not respuesta['es_correcta']:
                pregunta_id = str(respuesta['pregunta_id'])
                if pregunta_id not in preguntas_por_id:
                    preguntas_por_id[pregunta_id] = {
                        'pregunta': respuesta['pregunta'],
                        'ultima_fecha': respuesta['fecha']
                    }
                else:
                    # Actualizar con la fecha más reciente
                    if respuesta['fecha'] > preguntas_por_id[pregunta_id]['ultima_fecha']:
                        preguntas_por_id[pregunta_id]['ultima_fecha'] = respuesta['fecha']
        
        # Ordenar por número de fallos
        for pregunta_id, fallos in sorted(preguntas_falladas.items(), key=lambda x: x[1], reverse=True):
            if pregunta_id in preguntas_por_id:
                pregunta_info = preguntas_por_id[pregunta_id]
                fecha = datetime.datetime.fromisoformat(pregunta_info['ultima_fecha']).strftime("%d/%m/%Y")
                tree.insert('', 'end', values=(
                    pregunta_info['pregunta'],
                    fallos,
                    fecha
                ))
        
        tree.pack(fill='both', expand=True, padx=10, pady=10)
        
    def crear_grafico_niveles(self, parent):
        """Crea un gráfico de rendimiento por niveles"""
        try:
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
            
            fig, ax = plt.subplots(figsize=(8, 4))
            
            estadisticas = self.datos.get('estadisticas', {})
            preguntas_por_nivel = estadisticas.get('preguntas_por_nivel', {})
            
            niveles = []
            correctas = []
            incorrectas = []
            
            for nivel, stats in preguntas_por_nivel.items():
                if stats['correctas'] > 0 or stats['incorrectas'] > 0:
                    niveles.append(f"Nivel {nivel}")
                    correctas.append(stats['correctas'])
                    incorrectas.append(stats['incorrectas'])
            
            if niveles:
                x = np.arange(len(niveles))
                width = 0.35
                
                ax.bar(x - width/2, correctas, width, label='Correctas', color='lightgreen')
                ax.bar(x + width/2, incorrectas, width, label='Incorrectas', color='lightcoral')
                
                ax.set_xlabel('Niveles')
                ax.set_ylabel('Número de Preguntas')
                ax.set_title('Rendimiento por Nivel')
                ax.set_xticks(x)
                ax.set_xticklabels(niveles)
                ax.legend()
                
                canvas = FigureCanvasTkAgg(fig, parent)
                canvas.draw()
                canvas.get_tk_widget().pack(fill='both', expand=True)
            else:
                tk.Label(parent, text="No hay datos suficientes para mostrar el gráfico", 
                        font=('Arial', 12)).pack(pady=20)
                
        except ImportError:
            tk.Label(parent, text="Matplotlib no está instalado. No se puede mostrar el gráfico.", 
                    font=('Arial', 12)).pack(pady=20)
    
    def calcular_estadisticas_avanzadas(self, parent):
        """Calcula y muestra estadísticas avanzadas"""
        historial = self.datos.get('historial_respuestas', [])
        
        if not historial:
            tk.Label(parent, text="No hay datos suficientes", font=('Arial', 12)).pack(pady=20)
            return
        
        # Calcular tiempo promedio
        tiempos = [r['tiempo_respuesta_segundos'] for r in historial if r['tiempo_respuesta_segundos'] < 60]  # Filtrar tiempos anómalos
        tiempo_promedio = sum(tiempos) / len(tiempos) if tiempos else 0
        
        # Calcular progreso semanal
        ahora = datetime.datetime.now()
        hace_una_semana = ahora - datetime.timedelta(days=7)
        
        respuestas_semana = [r for r in historial if datetime.datetime.fromisoformat(r['fecha']) >= hace_una_semana]
        correctas_semana = sum(1 for r in respuestas_semana if r['es_correcta'])
        
        # Mostrar estadísticas
        stats_frame = tk.LabelFrame(parent, text="Estadísticas de la Semana", font=('Arial', 12, 'bold'))
        stats_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(stats_frame, text=f"Tiempo Promedio de Respuesta: {tiempo_promedio:.1f} segundos", 
                font=('Arial', 11)).pack(anchor='w', padx=10, pady=2)
        tk.Label(stats_frame, text=f"Preguntas Respondidas esta Semana: {len(respuestas_semana)}", 
                font=('Arial', 11)).pack(anchor='w', padx=10, pady=2)
        tk.Label(stats_frame, text=f"Respuestas Correctas esta Semana: {correctas_semana}", 
                font=('Arial', 11)).pack(anchor='w', padx=10, pady=2)
        
        if respuestas_semana:
            porcentaje_semana = (correctas_semana / len(respuestas_semana)) * 100
            tk.Label(stats_frame, text=f"Porcentaje de Aciertos esta Semana: {porcentaje_semana:.1f}%", 
                    font=('Arial', 11, 'bold'), 
                    fg='green' if porcentaje_semana >= 70 else 'orange').pack(anchor='w', padx=10, pady=2)
        
        # Racha actual
        estadisticas = self.datos.get('estadisticas', {})
        racha_actual = estadisticas.get('racha_actual_correctas', 0)
        mejor_racha = estadisticas.get('mejor_racha', 0)
        
        racha_frame = tk.LabelFrame(parent, text="Rachas", font=('Arial', 12, 'bold'))
        racha_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(racha_frame, text=f"Racha Actual: {racha_actual} respuestas correctas", 
                font=('Arial', 11)).pack(anchor='w', padx=10, pady=2)
        tk.Label(racha_frame, text=f"Mejor Racha: {mejor_racha} respuestas correctas", 
                font=('Arial', 11, 'bold'), fg='gold').pack(anchor='w', padx=10, pady=2)
    
    def ejecutar(self):
        """Ejecuta el panel de padres"""
        self.root.mainloop()

def main():
    """Función principal"""
    try:
        panel = PanelPadres()
        panel.ejecutar()
    except Exception as e:
        print(f"Error al iniciar el panel de padres: {e}")
        input("Presiona Enter para salir...")

if __name__ == "__main__":
    main()
