#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para crear el ejecutable del Lector Progresivo
Usa PyInstaller para generar un .exe standalone
"""

import os
import sys
import subprocess
from pathlib import Path


def install_requirements():
    """Instala las dependencias necesarias para crear el .exe"""
    requirements = [
        'pyinstaller',
        'psutil',
        'pywin32',
        'wmi'  # Para detección de hardware
    ]
    
    print("📦 Instalando dependencias para crear .exe...")
    for req in requirements:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', req])
            print(f"✅ {req} instalado")
        except subprocess.CalledProcessError:
            print(f"❌ Error instalando {req}")
            return False
    
    return True


def create_spec_file():
    """Crea el archivo .spec para PyInstaller con configuración personalizada"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# Análisis del archivo principal
a_lector = Analysis(
    ['lector.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.json', '.'),
        ('preguntas.json', '.'),
        ('recompensas.json', '.'),
        ('*.png', '.'),  # Imágenes si las hay
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'winsound',
        'keyboard',
        'psutil',
        'win32api',
        'win32con',
        'win32file',
        'wmi'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# Análisis del gestor de startup
a_startup = Analysis(
    ['startup_manager.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'psutil',
        'win32api',
        'win32con',
        'wmi',
        'winreg'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# Combinar análisis
MERGE((a_lector, 'lector', 'lector'), (a_startup, 'startup_manager', 'startup_manager'))

# Ejecutable principal (lector)
pyz_lector = PYZ(a_lector.pure, a_lector.zipped_data, cipher=block_cipher)

exe_lector = EXE(
    pyz_lector,
    a_lector.scripts,
    a_lector.binaries,
    a_lector.zipfiles,
    a_lector.datas,
    [],
    name='LectorProgresivo',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Sin ventana de consola
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
    version='version_info.txt' if os.path.exists('version_info.txt') else None,
)

# Ejecutable del gestor de startup
pyz_startup = PYZ(a_startup.pure, a_startup.zipped_data, cipher=block_cipher)

exe_startup = EXE(
    pyz_startup,
    a_startup.scripts,
    a_startup.binaries,
    a_startup.zipfiles,
    a_startup.datas,
    [],
    name='LectorProgresivo_Startup',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # Con ventana de consola para debug
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('lector_progresivo.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ Archivo .spec creado")


def create_version_info():
    """Crea archivo de información de versión para el .exe"""
    version_info = '''# UTF-8
#
# Para más detalles sobre fixed file info 'ffi' ver:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Lector Progresivo'),
        StringStruct(u'FileDescription', u'Asistente de Aprendizaje de Lectura'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'LectorProgresivo'),
        StringStruct(u'LegalCopyright', u'Copyright © 2024'),
        StringStruct(u'OriginalFilename', u'LectorProgresivo.exe'),
        StringStruct(u'ProductName', u'Lector Progresivo'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)'''
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_info)
    
    print("✅ Archivo de versión creado")


def build_executable():
    """Construye el ejecutable usando PyInstaller"""
    print("🔨 Construyendo ejecutable...")
    
    try:
        # Crear archivos de configuración
        create_spec_file()
        create_version_info()
        
        # Ejecutar PyInstaller
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            'lector_progresivo.spec'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Ejecutable creado exitosamente")
            print("📁 Archivos generados en la carpeta 'dist/'")
            
            # Verificar archivos generados
            dist_path = Path('dist')
            if dist_path.exists():
                exe_files = list(dist_path.glob('*.exe'))
                print(f"🎯 Ejecutables encontrados: {[f.name for f in exe_files]}")
            
            return True
        else:
            print("❌ Error creando ejecutable:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error durante la construcción: {e}")
        return False


def create_installer_script():
    """Crea un script de instalación que registra la aplicación en startup"""
    installer_content = '''@echo off
echo ========================================
echo    Instalador de Lector Progresivo
echo ========================================
echo.

REM Verificar permisos de administrador
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Ejecutando con permisos de administrador
) else (
    echo ⚠️  Se recomienda ejecutar como administrador
    echo    para registrar correctamente en el startup
    pause
)

echo 📁 Copiando archivos...
if not exist "%PROGRAMFILES%\\LectorProgresivo" mkdir "%PROGRAMFILES%\\LectorProgresivo"
copy "LectorProgresivo.exe" "%PROGRAMFILES%\\LectorProgresivo\\" >nul
copy "LectorProgresivo_Startup.exe" "%PROGRAMFILES%\\LectorProgresivo\\" >nul
copy "config.json" "%PROGRAMFILES%\\LectorProgresivo\\" >nul 2>nul
copy "preguntas.json" "%PROGRAMFILES%\\LectorProgresivo\\" >nul 2>nul
copy "recompensas.json" "%PROGRAMFILES%\\LectorProgresivo\\" >nul 2>nul

echo 🔧 Registrando en startup...
"%PROGRAMFILES%\\LectorProgresivo\\LectorProgresivo_Startup.exe" --register

echo ✅ Instalación completada
echo.
echo 🎮 Para ejecutar el juego manualmente:
echo    "%PROGRAMFILES%\\LectorProgresivo\\LectorProgresivo.exe"
echo.
echo 🔄 El juego se ejecutará automáticamente:
echo    - Al iniciar el sistema (después de 5 minutos)
echo    - Cada 2 horas si el sistema no está idle
echo.
echo 🛑 Para desinstalar:
echo    "%PROGRAMFILES%\\LectorProgresivo\\LectorProgresivo_Startup.exe" --unregister
echo.
pause
'''
    
    with open('instalar.bat', 'w', encoding='utf-8') as f:
        f.write(installer_content)
    
    print("✅ Script de instalación creado: instalar.bat")


def main():
    """Función principal del script de construcción"""
    print("🚀 Iniciando proceso de construcción del ejecutable")
    print("=" * 50)
    
    # Verificar que estamos en el directorio correcto
    if not Path('lector.py').exists():
        print("❌ Error: No se encontró lector.py en el directorio actual")
        print("   Asegúrate de ejecutar este script desde el directorio del proyecto")
        return
    
    # Instalar dependencias
    if not install_requirements():
        print("❌ Error instalando dependencias")
        return
    
    # Construir ejecutable
    if not build_executable():
        print("❌ Error construyendo ejecutable")
        return
    
    # Crear script de instalación
    create_installer_script()
    
    print("\n" + "=" * 50)
    print("🎉 ¡Proceso completado exitosamente!")
    print("\n📋 Próximos pasos:")
    print("1. Ve a la carpeta 'dist/'")
    print("2. Ejecuta 'instalar.bat' como administrador")
    print("3. El juego se registrará automáticamente en el startup")
    print("\n🎮 ¡Disfruta del Lector Progresivo!")


if __name__ == "__main__":
    main()
