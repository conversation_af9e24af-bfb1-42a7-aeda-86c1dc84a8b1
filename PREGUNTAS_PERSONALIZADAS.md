# 👨‍👩‍👧‍👦 Preguntas Personalizadas - Lector Progresivo

## 📋 Resumen de Implementación

Se han reemplazado completamente las preguntas genéricas por **10 preguntas personalizadas** específicas para la familia, con múltiples variaciones de respuesta para adaptarse a la escritura natural de los niños.

---

## 🏠 Categoría 1: Familia (5 preguntas)

### 👨 **Pregunta 1: ¿Cómo se llama tu papá?**
- **Respuesta principal:** César
- **Variaciones aceptadas:** 
  - `cesar`, `cezar`, `sesar`, `secar`
  - `papa cesar`, `mi papa cesar`, `se llama cesar`
  - Cualquier combinación de mayúsculas: `CESAR`, `<PERSON>sar`, `CeSaR`

### 👩 **Pregunta 2: ¿Cómo se llama tu mamá?**
- **Respuesta principal:** Diana
- **Variaciones aceptadas:**
  - `diana`, `dyana`, `dianna`
  - `mama diana`, `mi mama diana`, `se llama diana`
  - Cualquier combinación de mayúsculas: `DIANA`, `<PERSON>`, `DiAnA`

### 👵 **Pregunta 3: ¿Cómo se llama la mamita?**
- **Respuesta principal:** Rosa
- **Variaciones aceptadas:**
  - `rosa`, `roza`, `patricia`, `patrisia`
  - `mamita rosa`, `abuela rosa`, `se llama rosa`
  - `se llama patricia`, `mamita patricia`

### 👩‍🦱 **Pregunta 4: ¿Cuál es tu tía favorita?**
- **Respuesta principal:** Carolina
- **Variaciones aceptadas:**
  - `carolina`, `karolina`, `yury`, `yuri`
  - `tia carolina`, `tia yury`, `mi tia carolina`, `mi tia yury`

### 👧 **Pregunta 5: ¿Cómo se llama tu prima?**
- **Respuesta principal:** Manuela
- **Variaciones aceptadas:**
  - `manuela`, `manu`
  - `prima manuela`, `mi prima manuela`, `se llama manuela`

---

## 🔢 Categoría 2: Números y Mascotas (2 preguntas)

### 🐕 **Pregunta 6: ¿Cuántos perros tienes?**
- **Respuesta principal:** 5
- **Variaciones aceptadas:**
  - `5`, `cinco`, `sinco`
  - `tengo 5`, `tengo cinco`, `son 5`, `son cinco`

### 🏍️ **Pregunta 7: ¿Cuántas motos tiene tu papá?**
- **Respuesta principal:** 3
- **Variaciones aceptadas:**
  - `3`, `tres`, `trez`
  - `tiene 3`, `tiene tres`, `son 3`, `son tres`, `papa tiene 3`

---

## 💖 Categoría 3: Gustos Personales (2 preguntas)

### 🥋 **Pregunta 8: ¿Te gusta Goku?**
- **Respuesta principal:** Sí
- **Variaciones aceptadas:**
  - `si`, `sí`, `zi`, `yes`
  - `me gusta`, `si me gusta`, `claro`, `obvio`, `mucho`

### 🎮 **Pregunta 9: ¿Te gusta Minecraft?**
- **Respuesta principal:** Sí
- **Variaciones aceptadas:**
  - `si`, `sí`, `zi`, `yes`
  - `me gusta`, `si me gusta`, `claro`, `obvio`, `mucho`

---

## 🐒 Categoría 4: Conocimiento General (1 pregunta)

### 🍌 **Pregunta 10: ¿Qué comen los monos?**
- **Respuesta principal:** Bananos
- **Variaciones aceptadas:**
  - `bananos`, `bananas`, `platanos`, `plátanos`
  - `fruta`, `frutas`, `comen bananos`, `comen bananas`

---

## 🔤 Sistema de Tolerancia Ortográfica

### **Características Implementadas:**

1. **Ignorar Mayúsculas:** 
   - `CESAR` = `cesar` = `Cesar` = `CeSaR`

2. **Espacios Extra:**
   - `  cesar  ` = `cesar`

3. **Errores Comunes de Niños:**
   - `z` por `s`: `cezar` por `cesar`
   - `s` por `z`: `roza` por `rosa`
   - Omisión de acentos: `patricia` por `patricia`

4. **Respuestas Completas:**
   - Acepta tanto `cesar` como `mi papa se llama cesar`

5. **Números Flexibles:**
   - Acepta `5`, `cinco`, `sinco`
   - Acepta `3`, `tres`, `trez`

---

## 🎯 Beneficios de la Personalización

### **Para el Niño:**
- ✅ **Conexión emocional:** Reconoce nombres familiares
- ✅ **Motivación aumentada:** Las preguntas son relevantes para su vida
- ✅ **Menos frustración:** Sistema muy tolerante con errores
- ✅ **Aprendizaje natural:** Puede escribir como habla

### **Para la Familia:**
- ✅ **Contenido relevante:** Todas las preguntas son sobre su familia
- ✅ **Fácil de usar:** No importa cómo escriba el niño
- ✅ **Educativo:** Refuerza nombres y números familiares
- ✅ **Divertido:** Incluye gustos del niño (Goku, Minecraft)

---

## 🧪 Cómo Probar las Preguntas

### **Ejecutar Verificador:**
```bash
python test_preguntas_personalizadas.py
```

### **Pruebas Manuales Sugeridas:**

1. **Probar Variaciones de Nombres:**
   - Escribir `CESAR`, `cesar`, `Cesar`
   - Escribir `cezar`, `sesar` (errores comunes)
   - Escribir `papa cesar`, `mi papa cesar`

2. **Probar Números:**
   - Escribir `5`, `cinco`, `sinco`
   - Escribir `tengo 5 perros`, `son cinco`

3. **Probar Gustos:**
   - Escribir `SI`, `si`, `sí`, `zi`
   - Escribir `me gusta mucho`, `obvio`

4. **Probar Espacios y Mayúsculas:**
   - Escribir `  DIANA  ` (con espacios extra)
   - Escribir `DiAnA` (mayúsculas mezcladas)

---

## 📊 Estructura Técnica

### **Archivo de Configuración:**
```json
{
  "niveles": {
    "1_familia": {
      "descripcion": "Preguntas sobre la familia",
      "preguntas": [
        {
          "id": 1,
          "pregunta": "¿Cómo se llama tu papá?",
          "respuesta_esperada": "cesar",
          "alternativas": ["cesar", "cezar", "sesar", "papa cesar", ...]
        }
      ]
    }
  }
}
```

### **Procesamiento de Respuestas:**
```python
# Normalización automática
respuesta_usuario_limpia = respuesta_usuario.strip().lower()
respuesta_correcta = pregunta['respuesta_esperada'].lower().strip()
alternativas = [alt.lower().strip() for alt in pregunta['alternativas']]

# Verificación flexible
es_correcta = (respuesta_usuario_limpia == respuesta_correcta or
               respuesta_usuario_limpia in alternativas)
```

---

## 🔄 Compatibilidad y Migración

- ✅ **Datos existentes:** El progreso anterior se mantiene
- ✅ **Configuración:** No requiere cambios adicionales
- ✅ **Sistema de recompensas:** Funciona igual con las nuevas preguntas
- ✅ **Panel de padres:** Muestra estadísticas de las preguntas personalizadas

---

## 🎉 Resultado Final

**10 preguntas completamente personalizadas** que harán que el niño se sienta más conectado con el juego, ya que todas las preguntas son sobre su familia, sus gustos y su entorno familiar específico.

El sistema es **extremadamente tolerante** con errores de ortografía y diferentes formas de escribir, permitiendo que el niño se enfoque en el aprendizaje sin frustrarse por la escritura.

---

**¡Las preguntas personalizadas están listas para usar! 👨‍👩‍👧‍👦**
