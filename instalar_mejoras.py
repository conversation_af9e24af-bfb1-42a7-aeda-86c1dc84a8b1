#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de instalación para las mejoras del Lector Progresivo
"""

import json
import shutil
import subprocess
import sys
from pathlib import Path

def crear_archivo_recompensas():
    """Crea el archivo de recompensas inicial"""
    recompensas_data = {
        "stickers_desbloqueados": [],
        "fecha_ultimo_reinicio": "",
        "celebraciones_disponibles": [
            "¡Excelente trabajo! 🌟",
            "¡Eres increíble! 🎉",
            "¡Sigue así! 💪",
            "¡Fantástico! 🎊",
            "¡Muy bien! 👏",
            "¡Genial! ⭐",
            "¡Perfecto! 🏆",
            "¡Súper! 🚀"
        ],
        "stickers_disponibles": [
            {"id": 1, "nombre": "Estrella Dorada", "emoji": "⭐", "costo": 5, "desbloqueado": False},
            {"id": 2, "nombre": "Corazón", "emoji": "❤️", "costo": 3, "desbloqueado": False},
            {"id": 3, "nombre": "Trofeo", "emoji": "🏆", "costo": 10, "desbloqueado": False},
            {"id": 4, "nombre": "Cohete", "emoji": "🚀", "costo": 8, "desbloqueado": False},
            {"id": 5, "nombre": "Arcoíris", "emoji": "🌈", "costo": 6, "desbloqueado": False},
            {"id": 6, "nombre": "Unicornio", "emoji": "🦄", "costo": 15, "desbloqueado": False},
            {"id": 7, "nombre": "Corona", "emoji": "👑", "costo": 12, "desbloqueado": False},
            {"id": 8, "nombre": "Diamante", "emoji": "💎", "costo": 20, "desbloqueado": False}
        ]
    }
    
    with open('recompensas.json', 'w', encoding='utf-8') as f:
        json.dump(recompensas_data, f, indent=2, ensure_ascii=False)
    
    print("✅ Archivo de recompensas creado")

def crear_backup_archivos():
    """Crea backup de archivos importantes"""
    archivos_backup = ['respuestas_guardadas.json', 'config.json']
    
    for archivo in archivos_backup:
        if Path(archivo).exists():
            backup_name = f"{archivo}.backup"
            shutil.copy2(archivo, backup_name)
            print(f"✅ Backup creado: {backup_name}")

def actualizar_preguntas():
    """Actualiza el archivo de preguntas si existe el expandido"""
    if Path('preguntas_expandidas.json').exists():
        if Path('preguntas.json').exists():
            shutil.copy2('preguntas.json', 'preguntas_original.json')
            print("✅ Backup de preguntas originales creado")
        
        shutil.copy2('preguntas_expandidas.json', 'preguntas.json')
        print("✅ Preguntas expandidas aplicadas")

def verificar_dependencias():
    """Verifica e instala dependencias opcionales"""
    dependencias_opcionales = {
        'matplotlib': 'Para gráficos en el panel de padres',
        'numpy': 'Para cálculos estadísticos'
    }
    
    print("\n🔍 Verificando dependencias opcionales...")
    
    for dep, descripcion in dependencias_opcionales.items():
        try:
            __import__(dep)
            print(f"✅ {dep} ya está instalado")
        except ImportError:
            print(f"❌ {dep} no está instalado ({descripcion})")
            respuesta = input(f"¿Instalar {dep}? (s/n): ").lower().strip()
            if respuesta in ['s', 'si', 'sí', 'y', 'yes']:
                try:
                    subprocess.run([sys.executable, '-m', 'pip', 'install', dep], 
                                 check=True, capture_output=True)
                    print(f"✅ {dep} instalado correctamente")
                except subprocess.CalledProcessError:
                    print(f"❌ Error al instalar {dep}")

def crear_accesos_directos():
    """Crea scripts de acceso directo"""
    
    # Script para ejecutar la aplicación principal
    script_principal = """@echo off
cd /d "%~dp0"
python lector.py
pause
"""
    
    with open('Ejecutar_Lector.bat', 'w', encoding='utf-8') as f:
        f.write(script_principal)
    
    # Script para el panel de padres
    script_padres = """@echo off
cd /d "%~dp0"
python panel_padres_mejorado.py
pause
"""
    
    with open('Panel_Padres.bat', 'w', encoding='utf-8') as f:
        f.write(script_padres)
    
    # Script de pruebas
    script_pruebas = """@echo off
cd /d "%~dp0"
python test_mejoras.py
pause
"""

    with open('Probar_Mejoras.bat', 'w', encoding='utf-8') as f:
        f.write(script_pruebas)

    # Script de pruebas de bugs
    script_bugs = """@echo off
cd /d "%~dp0"
python test_bugs_corregidos.py
pause
"""

    with open('Probar_Bugs_Corregidos.bat', 'w', encoding='utf-8') as f:
        f.write(script_bugs)

    # Script de pruebas de preguntas personalizadas
    script_preguntas = """@echo off
cd /d "%~dp0"
python test_preguntas_personalizadas.py
pause
"""

    with open('Probar_Preguntas_Personalizadas.bat', 'w', encoding='utf-8') as f:
        f.write(script_preguntas)
    
    print("✅ Scripts de acceso directo creados")

def mostrar_resumen():
    """Muestra un resumen de las mejoras instaladas"""
    print("\n" + "="*60)
    print("🎉 INSTALACIÓN DE MEJORAS COMPLETADA")
    print("="*60)
    
    print("\n📋 MEJORAS IMPLEMENTADAS:")
    mejoras = [
        "Funcionalidad de sonido removida",
        "Bug de spam de Enter corregido",
        "Inteligencia adaptativa (ayuda después de 3 fallos)",
        "Panel de seguimiento para padres",
        "Sistema de recompensas con stickers virtuales",
        "Diseño más infantil y colorido",
        "Banco de preguntas expandido",
        "Reinicio semanal automático de estrellitas",
        "Celebraciones aleatorias mejoradas",
        "Botón de salir que se desbloquea automáticamente",
        "Botón de ayuda con restricciones de tiempo/fallos",
        "Stickers integrados en ventana principal",
        "Sonidos especiales para todas las interacciones"
    ]
    
    for i, mejora in enumerate(mejoras, 1):
        print(f"  {i}. ✅ {mejora}")
    
    print("\n🚀 ARCHIVOS CREADOS:")
    archivos_nuevos = [
        "panel_padres_mejorado.py - Panel de seguimiento para padres",
        "sistema_recompensas_mejorado.py - Sistema de stickers y recompensas",
        "preguntas_expandidas.json - Banco de preguntas ampliado",
        "test_mejoras.py - Herramienta de pruebas",
        "recompensas.json - Datos de stickers y recompensas",
        "Ejecutar_Lector.bat - Acceso directo a la aplicación",
        "Panel_Padres.bat - Acceso directo al panel de padres",
        "Probar_Mejoras.bat - Acceso directo a las pruebas",
        "Probar_Bugs_Corregidos.bat - Verificación de bugs corregidos",
        "Probar_Preguntas_Personalizadas.bat - Verificación de preguntas familiares"
    ]
    
    for archivo in archivos_nuevos:
        print(f"  📄 {archivo}")
    
    print("\n🎮 CÓMO USAR:")
    print("  1. Ejecuta 'Ejecutar_Lector.bat' para usar la aplicación mejorada")
    print("  2. Ejecuta 'Panel_Padres.bat' para ver el progreso del niño")
    print("  3. Ejecuta 'Probar_Mejoras.bat' para probar todas las funciones")
    print("  4. Ejecuta 'Probar_Bugs_Corregidos.bat' para verificar las correcciones")
    print("  5. Ejecuta 'Probar_Preguntas_Personalizadas.bat' para ver las preguntas familiares")
    
    print("\n💡 CARACTERÍSTICAS DESTACADAS:")
    print("  • El sistema ahora previene el spam de Enter")
    print("  • Ayuda automática después de 3 fallos en la misma pregunta")
    print("  • Stickers desbloqueables con estrellitas")
    print("  • Panel detallado para que los padres vean el progreso")
    print("  • Reinicio semanal automático para mantener la motivación")
    print("  • Botón de salir que aparece después de 10 respuestas correctas")
    print("  • Ayuda restringida hasta 5 minutos o 5 fallos")
    print("  • Stickers integrados en la ventana principal")
    print("  • Sonidos especiales para todas las interacciones")
    print("  • Preguntas personalizadas con nombres familiares específicos")
    print("  • Sistema tolerante con errores de ortografía de niños")
    
    print("\n" + "="*60)

def main():
    """Función principal de instalación"""
    print("🔧 INSTALADOR DE MEJORAS - LECTOR PROGRESIVO")
    print("="*50)
    
    try:
        print("\n1. Creando backup de archivos importantes...")
        crear_backup_archivos()
        
        print("\n2. Creando archivo de recompensas...")
        crear_archivo_recompensas()
        
        print("\n3. Actualizando banco de preguntas...")
        actualizar_preguntas()
        
        print("\n4. Creando accesos directos...")
        crear_accesos_directos()
        
        print("\n5. Verificando dependencias...")
        verificar_dependencias()
        
        mostrar_resumen()
        
        print("\n✨ ¡Instalación completada exitosamente!")
        input("\nPresiona Enter para continuar...")
        
    except Exception as e:
        print(f"\n❌ Error durante la instalación: {e}")
        input("Presiona Enter para salir...")

if __name__ == "__main__":
    main()
