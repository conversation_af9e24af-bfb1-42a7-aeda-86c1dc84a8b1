#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de prueba para verificar que los bugs han sido corregidos
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import sys
import time
from pathlib import Path

class TestBugsCorregidos:
    def __init__(self):
        self.directorio_base = Path(__file__).parent
        self.crear_ventana_test()
        
    def crear_ventana_test(self):
        """Crea la ventana de pruebas"""
        self.root = tk.Tk()
        self.root.title("🐛 Verificación de Bugs Corregidos")
        self.root.geometry("700x600")
        self.root.configure(bg='#F0F8FF')
        
        # Título
        tk.Label(self.root, 
                text="🐛 Verificación de Bugs Corregidos", 
                font=('Arial', 18, 'bold'),
                bg='#F0F8FF',
                fg='#2F4F4F').pack(pady=20)
        
        # Frame principal
        main_frame = tk.Frame(self.root, bg='#F0F8FF')
        main_frame.pack(pady=20, padx=20, fill='both', expand=True)
        
        # Sección 1: Bug del botón de salir
        self.crear_seccion_bug1(main_frame)
        
        # Sección 2: Bug del botón de ayuda
        self.crear_seccion_bug2(main_frame)
        
        # Sección 3: Bug de la ventana de stickers
        self.crear_seccion_bug3(main_frame)
        
        # Sección 4: Sonidos agregados
        self.crear_seccion_sonidos(main_frame)
        
        # Botón para ejecutar aplicación
        tk.Button(self.root,
                 text="🚀 Ejecutar Aplicación para Probar",
                 font=('Arial', 14, 'bold'),
                 bg='#87CEEB',
                 fg='#2F4F4F',
                 padx=30,
                 pady=15,
                 command=self.ejecutar_aplicacion).pack(pady=20)
    
    def crear_seccion_bug1(self, parent):
        """Crea la sección para el bug del botón de salir"""
        frame = tk.LabelFrame(parent, text="🚪 Bug 1: Botón de Salir", 
                             font=('Arial', 12, 'bold'), bg='#F0F8FF')
        frame.pack(fill='x', pady=10)
        
        tk.Label(frame, 
                text="❌ ANTES: No había forma de salir fácilmente del programa",
                font=('Arial', 10),
                fg='red',
                bg='#F0F8FF').pack(anchor='w', padx=10, pady=2)
        
        tk.Label(frame, 
                text="✅ AHORA: Botón de salir se desbloquea después de:",
                font=('Arial', 10, 'bold'),
                fg='green',
                bg='#F0F8FF').pack(anchor='w', padx=10, pady=2)
        
        tk.Label(frame, 
                text="   • 10 respuestas correctas, O",
                font=('Arial', 10),
                bg='#F0F8FF').pack(anchor='w', padx=20, pady=1)
        
        tk.Label(frame, 
                text="   • 15 minutos de juego",
                font=('Arial', 10),
                bg='#F0F8FF').pack(anchor='w', padx=20, pady=1)
        
        tk.Label(frame, 
                text="   • Aparece un botón grande rojo '🚪 Salir' en la esquina",
                font=('Arial', 10),
                bg='#F0F8FF').pack(anchor='w', padx=20, pady=1)
    
    def crear_seccion_bug2(self, parent):
        """Crea la sección para el bug del botón de ayuda"""
        frame = tk.LabelFrame(parent, text="💡 Bug 2: Botón de Ayuda Restringido", 
                             font=('Arial', 12, 'bold'), bg='#F0F8FF')
        frame.pack(fill='x', pady=10)
        
        tk.Label(frame, 
                text="❌ ANTES: Ayuda disponible inmediatamente (muy fácil)",
                font=('Arial', 10),
                fg='red',
                bg='#F0F8FF').pack(anchor='w', padx=10, pady=2)
        
        tk.Label(frame, 
                text="✅ AHORA: Ayuda se desbloquea solo después de:",
                font=('Arial', 10, 'bold'),
                fg='green',
                bg='#F0F8FF').pack(anchor='w', padx=10, pady=2)
        
        tk.Label(frame, 
                text="   • 5 minutos de juego, O",
                font=('Arial', 10),
                bg='#F0F8FF').pack(anchor='w', padx=20, pady=1)
        
        tk.Label(frame, 
                text="   • 5 respuestas incorrectas",
                font=('Arial', 10),
                bg='#F0F8FF').pack(anchor='w', padx=20, pady=1)
        
        tk.Label(frame, 
                text="   • Muestra contador de tiempo/fallos restantes",
                font=('Arial', 10),
                bg='#F0F8FF').pack(anchor='w', padx=20, pady=1)
    
    def crear_seccion_bug3(self, parent):
        """Crea la sección para el bug de la ventana de stickers"""
        frame = tk.LabelFrame(parent, text="🌟 Bug 3: Ventana de Stickers", 
                             font=('Arial', 12, 'bold'), bg='#F0F8FF')
        frame.pack(fill='x', pady=10)
        
        tk.Label(frame, 
                text="❌ ANTES: Se abría ventana separada que no se podía ver",
                font=('Arial', 10),
                fg='red',
                bg='#F0F8FF').pack(anchor='w', padx=10, pady=2)
        
        tk.Label(frame, 
                text="✅ AHORA: Stickers se muestran integrados en la ventana principal:",
                font=('Arial', 10, 'bold'),
                fg='green',
                bg='#F0F8FF').pack(anchor='w', padx=10, pady=2)
        
        tk.Label(frame, 
                text="   • Click en '🌟 Mis Stickers' muestra/oculta la colección",
                font=('Arial', 10),
                bg='#F0F8FF').pack(anchor='w', padx=20, pady=1)
        
        tk.Label(frame, 
                text="   • Botón cambia a '❌ Cerrar Stickers' cuando está abierto",
                font=('Arial', 10),
                bg='#F0F8FF').pack(anchor='w', padx=20, pady=1)
        
        tk.Label(frame, 
                text="   • Área scrolleable con 4 stickers por fila",
                font=('Arial', 10),
                bg='#F0F8FF').pack(anchor='w', padx=20, pady=1)
    
    def crear_seccion_sonidos(self, parent):
        """Crea la sección para los sonidos agregados"""
        frame = tk.LabelFrame(parent, text="🔊 Mejora: Sonidos Especiales", 
                             font=('Arial', 12, 'bold'), bg='#F0F8FF')
        frame.pack(fill='x', pady=10)
        
        tk.Label(frame, 
                text="🎵 NUEVO: Sonidos especiales para hacer el programa más atractivo:",
                font=('Arial', 10, 'bold'),
                fg='blue',
                bg='#F0F8FF').pack(anchor='w', padx=10, pady=2)
        
        sonidos = [
            "🔘 Click en botones: Sonido suave de confirmación",
            "✅ Respuesta correcta: Tono alegre y positivo",
            "❌ Respuesta incorrecta: Tono suave de 'inténtalo de nuevo'",
            "🌟 Sticker desbloqueado: Secuencia mágica ascendente",
            "🎉 Celebración especial: Fanfarria de logro",
            "🔓 Desbloqueo: Sonido de logro conseguido",
            "🚪 Salir: Melodía de despedida"
        ]
        
        for sonido in sonidos:
            tk.Label(frame, 
                    text=f"   • {sonido}",
                    font=('Arial', 9),
                    bg='#F0F8FF').pack(anchor='w', padx=20, pady=1)
    
    def ejecutar_aplicacion(self):
        """Ejecuta la aplicación principal para probar"""
        try:
            subprocess.Popen([sys.executable, 'lector.py'])
            
            # Mostrar instrucciones de prueba
            instrucciones = """
🧪 INSTRUCCIONES DE PRUEBA:

1. 🚪 BOTÓN DE SALIR:
   • Responde 10 preguntas correctas O espera 15 minutos
   • Verifica que aparezca el botón rojo "🚪 Salir"
   • Prueba que funcione correctamente

2. 💡 BOTÓN DE AYUDA:
   • Al inicio, el botón debe mostrar tiempo/fallos restantes
   • Falla 5 preguntas O espera 5 minutos
   • Verifica que se desbloquee la ayuda real

3. 🌟 STICKERS INTEGRADOS:
   • Click en "🌟 Mis Stickers"
   • Verifica que se muestre en la misma ventana
   • Click en "❌ Cerrar Stickers" para ocultar

4. 🔊 SONIDOS:
   • Escucha sonidos al hacer click en botones
   • Responde correcta/incorrectamente para oír sonidos
   • Desbloquea stickers para oír sonidos especiales

¡Prueba todas las funciones!
            """
            
            messagebox.showinfo("Instrucciones de Prueba", instrucciones)
            
        except Exception as e:
            messagebox.showerror("Error", f"Error al ejecutar aplicación: {e}")
    
    def ejecutar(self):
        """Ejecuta la ventana de pruebas"""
        self.root.mainloop()

def main():
    """Función principal"""
    try:
        test = TestBugsCorregidos()
        test.ejecutar()
    except Exception as e:
        print(f"Error al iniciar pruebas: {e}")
        input("Presiona Enter para salir...")

if __name__ == "__main__":
    main()
