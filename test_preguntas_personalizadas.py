#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de prueba para las preguntas personalizadas
"""

import tkinter as tk
from tkinter import messagebox, scrolledtext
import json
import subprocess
import sys
from pathlib import Path

class TestPreguntasPersonalizadas:
    def __init__(self):
        self.directorio_base = Path(__file__).parent
        self.cargar_preguntas()
        self.crear_ventana_test()
        
    def cargar_preguntas(self):
        """Carga las preguntas personalizadas"""
        try:
            with open(self.directorio_base / 'preguntas.json', 'r', encoding='utf-8') as f:
                self.preguntas_data = json.load(f)
        except FileNotFoundError:
            messagebox.showerror("Error", "No se encontró el archivo preguntas.json")
            return
        
    def crear_ventana_test(self):
        """Crea la ventana de pruebas"""
        self.root = tk.Tk()
        self.root.title("👨‍👩‍👧‍👦 Preguntas Personalizadas - Verificación")
        self.root.geometry("900x700")
        self.root.configure(bg='#F0F8FF')
        
        # Título
        tk.Label(self.root, 
                text="👨‍👩‍👧‍👦 Preguntas Personalizadas Implementadas", 
                font=('Arial', 18, 'bold'),
                bg='#F0F8FF',
                fg='#2F4F4F').pack(pady=20)
        
        # Frame principal
        main_frame = tk.Frame(self.root, bg='#F0F8FF')
        main_frame.pack(pady=10, padx=20, fill='both', expand=True)
        
        # Mostrar resumen
        self.mostrar_resumen(main_frame)
        
        # Mostrar todas las preguntas
        self.mostrar_preguntas_detalle(main_frame)
        
        # Botones de acción
        self.crear_botones_accion()
    
    def mostrar_resumen(self, parent):
        """Muestra un resumen de las preguntas"""
        resumen_frame = tk.LabelFrame(parent, text="📊 Resumen de Preguntas", 
                                     font=('Arial', 12, 'bold'), bg='#F0F8FF')
        resumen_frame.pack(fill='x', pady=10)
        
        total_preguntas = 0
        for nivel in self.preguntas_data['niveles'].values():
            total_preguntas += len(nivel['preguntas'])
        
        info_text = f"""
✅ Total de preguntas personalizadas: {total_preguntas}
✅ Niveles configurados: {len(self.preguntas_data['niveles'])}
✅ Respuestas múltiples para errores de ortografía: SÍ
✅ Ignorar mayúsculas: SÍ
✅ Preguntas familiares específicas: SÍ

📋 Categorías:
• Familia (papá, mamá, mamita, tía, prima)
• Números y mascotas (perros, motos)
• Gustos personales (Goku, Minecraft)
• Conocimiento general (monos)
        """
        
        tk.Label(resumen_frame, 
                text=info_text,
                font=('Arial', 10),
                bg='#F0F8FF',
                justify='left').pack(anchor='w', padx=10, pady=10)
    
    def mostrar_preguntas_detalle(self, parent):
        """Muestra el detalle de todas las preguntas"""
        detalle_frame = tk.LabelFrame(parent, text="📝 Detalle de Preguntas y Respuestas", 
                                     font=('Arial', 12, 'bold'), bg='#F0F8FF')
        detalle_frame.pack(fill='both', expand=True, pady=10)
        
        # Crear área de texto scrolleable
        text_area = scrolledtext.ScrolledText(detalle_frame, 
                                            width=80, 
                                            height=20,
                                            font=('Courier', 10),
                                            bg='white')
        text_area.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Llenar con información de preguntas
        contenido = ""
        for nivel_key, nivel_data in self.preguntas_data['niveles'].items():
            contenido += f"\n{'='*60}\n"
            contenido += f"📂 {nivel_key.upper()}: {nivel_data['descripcion']}\n"
            contenido += f"{'='*60}\n\n"
            
            for pregunta in nivel_data['preguntas']:
                contenido += f"❓ PREGUNTA {pregunta['id']}: {pregunta['pregunta']}\n"
                contenido += f"✅ RESPUESTA PRINCIPAL: {pregunta['respuesta_esperada']}\n"
                contenido += f"📝 ALTERNATIVAS ACEPTADAS:\n"
                
                for i, alt in enumerate(pregunta['alternativas'], 1):
                    contenido += f"   {i}. {alt}\n"
                
                contenido += f"🎯 DIFICULTAD: {pregunta['dificultad']}\n"
                contenido += f"🖼️ IMAGEN: {pregunta['imagen']}\n"
                contenido += "\n" + "-"*50 + "\n\n"
        
        text_area.insert('1.0', contenido)
        text_area.config(state='disabled')  # Solo lectura
    
    def crear_botones_accion(self):
        """Crea los botones de acción"""
        botones_frame = tk.Frame(self.root, bg='#F0F8FF')
        botones_frame.pack(pady=20)
        
        # Botón para probar aplicación
        tk.Button(botones_frame,
                 text="🎮 Probar Aplicación con Preguntas Personalizadas",
                 font=('Arial', 12, 'bold'),
                 bg='#87CEEB',
                 fg='#2F4F4F',
                 padx=20,
                 pady=10,
                 command=self.probar_aplicacion).pack(side='left', padx=10)
        
        # Botón para mostrar consejos
        tk.Button(botones_frame,
                 text="💡 Consejos de Uso",
                 font=('Arial', 12, 'bold'),
                 bg='#90EE90',
                 fg='#2F4F4F',
                 padx=20,
                 pady=10,
                 command=self.mostrar_consejos).pack(side='left', padx=10)
        
        # Botón para verificar ortografía
        tk.Button(botones_frame,
                 text="🔤 Probar Variaciones de Ortografía",
                 font=('Arial', 12, 'bold'),
                 bg='#FFB6C1',
                 fg='#2F4F4F',
                 padx=20,
                 pady=10,
                 command=self.probar_ortografia).pack(side='left', padx=10)
    
    def probar_aplicacion(self):
        """Ejecuta la aplicación principal"""
        try:
            subprocess.Popen([sys.executable, 'lector.py'])
            messagebox.showinfo("Aplicación Iniciada", 
                              "¡Aplicación iniciada con preguntas personalizadas!\n\n" +
                              "Ahora puedes probar todas las preguntas familiares.")
        except Exception as e:
            messagebox.showerror("Error", f"Error al iniciar aplicación: {e}")
    
    def mostrar_consejos(self):
        """Muestra consejos para usar las preguntas personalizadas"""
        consejos = """
🎯 CONSEJOS PARA USAR LAS PREGUNTAS PERSONALIZADAS:

👨‍👩‍👧‍👦 PREGUNTAS FAMILIARES:
• Las preguntas están adaptadas específicamente para tu familia
• Incluyen nombres reales: César, Diana, Rosa/Patricia, Carolina/Yury, Manuela
• El niño se sentirá más conectado al reconocer nombres familiares

🔤 VARIACIONES DE ORTOGRAFÍA:
• El sistema acepta múltiples formas de escribir cada respuesta
• Ejemplos: "cesar", "cezar", "sesar" - todas son válidas
• No importan las mayúsculas: "CESAR", "Cesar", "cesar" - todas funcionan

📝 RESPUESTAS FLEXIBLES:
• Se aceptan respuestas completas: "mi papá se llama césar"
• También respuestas cortas: "césar"
• Números en cifras o letras: "5" o "cinco"

🎮 GUSTOS PERSONALES:
• Preguntas sobre Goku y Minecraft harán el juego más divertido
• Respuestas como "sí", "si", "zi", "me gusta" - todas válidas

🐒 CONOCIMIENTO GENERAL:
• Pregunta sobre monos para enseñar sobre animales
• Acepta "bananos", "bananas", "plátanos", "fruta"

💡 RECOMENDACIONES:
• Deja que el niño escriba naturalmente
• No corrijas su ortografía durante el juego
• El sistema es muy tolerante con errores comunes
• Celebra cada respuesta correcta, sin importar cómo la escriba
        """
        
        ventana_consejos = tk.Toplevel(self.root)
        ventana_consejos.title("💡 Consejos de Uso")
        ventana_consejos.geometry("600x500")
        ventana_consejos.configure(bg='#F0F8FF')
        
        text_consejos = scrolledtext.ScrolledText(ventana_consejos, 
                                                font=('Arial', 10),
                                                bg='white')
        text_consejos.pack(fill='both', expand=True, padx=20, pady=20)
        text_consejos.insert('1.0', consejos)
        text_consejos.config(state='disabled')
    
    def probar_ortografia(self):
        """Muestra ejemplos de variaciones de ortografía aceptadas"""
        ejemplos = """
🔤 EJEMPLOS DE VARIACIONES DE ORTOGRAFÍA ACEPTADAS:

👨 PAPÁ (César):
✅ cesar, cezar, sesar, secar
✅ papa cesar, mi papa cesar, se llama cesar
✅ CESAR, Cesar, CeSaR (cualquier combinación de mayúsculas)

👩 MAMÁ (Diana):
✅ diana, dyana, dianna
✅ mama diana, mi mama diana, se llama diana
✅ DIANA, Diana, DiAnA

👵 MAMITA (Rosa/Patricia):
✅ rosa, roza, patricia, patrisia
✅ mamita rosa, abuela rosa, se llama rosa
✅ se llama patricia, mamita patricia

👩‍🦱 TÍA FAVORITA (Carolina/Yury):
✅ carolina, karolina, yury, yuri
✅ tia carolina, tia yury, mi tia carolina
✅ CAROLINA, Carolina, YURY, Yury

👧 PRIMA (Manuela):
✅ manuela, manu
✅ prima manuela, mi prima manuela, se llama manuela
✅ MANUELA, Manuela, ManUeLa

🔢 NÚMEROS:
✅ 5, cinco, sinco, tengo 5, tengo cinco, son 5
✅ 3, tres, trez, tiene 3, tiene tres, papa tiene 3

👍 GUSTOS (Sí):
✅ si, sí, zi, yes, me gusta, si me gusta, claro, obvio, mucho
✅ SI, Si, SÍ, YES, ME GUSTA

🐒 MONOS:
✅ bananos, bananas, platanos, plátanos, fruta, frutas
✅ comen bananos, comen bananas
✅ BANANOS, Bananos, BaNaNoS

💡 NOTA: El sistema es MUY tolerante y acepta errores comunes de niños.
        """
        
        ventana_ortografia = tk.Toplevel(self.root)
        ventana_ortografia.title("🔤 Variaciones de Ortografía")
        ventana_ortografia.geometry("700x600")
        ventana_ortografia.configure(bg='#F0F8FF')
        
        text_ortografia = scrolledtext.ScrolledText(ventana_ortografia, 
                                                  font=('Courier', 9),
                                                  bg='white')
        text_ortografia.pack(fill='both', expand=True, padx=20, pady=20)
        text_ortografia.insert('1.0', ejemplos)
        text_ortografia.config(state='disabled')
    
    def ejecutar(self):
        """Ejecuta la ventana de pruebas"""
        self.root.mainloop()

def main():
    """Función principal"""
    try:
        test = TestPreguntasPersonalizadas()
        test.ejecutar()
    except Exception as e:
        print(f"Error al iniciar pruebas: {e}")
        input("Presiona Enter para salir...")

if __name__ == "__main__":
    main()
