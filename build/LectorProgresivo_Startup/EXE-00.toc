('C:\\Users\\<USER>\\Documents\\augment-projects\\ConsoleReader\\lector_progresivo\\dist\\LectorProgresivo_Startup.exe',
 True,
 False,
 False,
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\bootloader\\images\\icon-console.ico',
 None,
 False,
 False,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'C:\\Users\\<USER>\\Documents\\augment-projects\\ConsoleReader\\lector_progresivo\\build\\LectorProgresivo_Startup\\LectorProgresivo_Startup.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\ConsoleReader\\lector_progresivo\\build\\LectorProgresivo_Startup\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\ConsoleReader\\lector_progresivo\\build\\LectorProgresivo_Startup\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\ConsoleReader\\lector_progresivo\\build\\LectorProgresivo_Startup\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\ConsoleReader\\lector_progresivo\\build\\LectorProgresivo_Startup\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\ConsoleReader\\lector_progresivo\\build\\LectorProgresivo_Startup\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\ConsoleReader\\lector_progresivo\\build\\LectorProgresivo_Startup\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('startup_manager',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\ConsoleReader\\lector_progresivo\\startup_manager.py',
   'PYSOURCE'),
  ('python313.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\python313.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes313.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\pywin32_system32\\pywintypes313.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom313.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\pywin32_system32\\pythoncom313.dll',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\win32event.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\win32\\win32event.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('win32\\win32file.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\win32\\win32file.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\VCRUNTIME140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\python3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\ConsoleReader\\lector_progresivo\\build\\LectorProgresivo_Startup\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 1750228828,
 [('run.exe',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\run.exe',
   'EXECUTABLE')],
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\python313.dll')
